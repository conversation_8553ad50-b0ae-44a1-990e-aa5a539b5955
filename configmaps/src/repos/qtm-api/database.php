<?php
return [
    'default' => 'mysql',
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'write' => [
                'host' => '127.0.0.1',
                'port' => '3306'
            ],
            'read' => [
                'host' => '127.0.0.1',
                'port' => '3307',
                'options'   => [
                    PDO::ATTR_PERSISTENT => true,
                ],
            ],
            'database' => 'qtm',
            'username' => 'root',
            'password' => 'qtmP@ssw0rd',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
        ],
    ],
    'migrations' => 'migrations',
    'redis' => [
        'client' => 'predis',
        'default' => [
            'host' => 'redis.default.svc.cluster.local',
            'password' => null,
            'port' => '6379',
            'database' => 0,
        ],
    ],
];
