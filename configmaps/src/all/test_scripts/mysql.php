<?php
    $servername = "127.0.0.1";
    $username = "admin";
    $password = "qtmP@ssw0rd";
    $dbname = "qtm";
    $readPorts = [
        '3307',
        '3308',
        '3309'
    ];
    $port = $readPorts[array_rand($readPorts)];
    try {
        $conn = new PDO("mysql:host=$servername;port=$port;dbname=$dbname", $username, $password, [
            PDO::ATTR_PERSISTENT => true
        ]);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $stmt = $conn->prepare("SELECT customer_id FROM oc_customer"); 
        $stmt->execute(); 
        $result = $stmt->fetchAll();
        print_r($result);
    }
    catch(PDOException $e) {
        echo "Error: " . $e->getMessage();
    }
    $conn = null;