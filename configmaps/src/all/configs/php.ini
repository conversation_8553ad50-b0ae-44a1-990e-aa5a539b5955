; 上傳大小、時間相關的
; max_execution_time
; max_input_time
; memory_limit
; post_max_size
; upload_max_filesize
; default_socket_timeout
; mysql.connect_timeout

[PHP]
expose_php = Off ; disable X-Powered-By: PHP
max_execution_time = 86400
max_input_time = -1
memory_limit = 1024M
realpath_cache_ttl = 600
output_buffering = 4096
report_memleaks=On
log_errors_max_len=1024
max_file_uploads=20
upload_max_filesize=200M
post_max_size=200M
default_charset = "utf-8"
max_input_vars=3000
log_errors = On
error_log = "/var/www/html"

[Date]
; date.timezone = "Asia/Taipei"
[opcache]
opcache.enable=1
opcache.huge_code_pages=1
opcache.max_file_size=2M
opcache.memory_consumption=512
[Pdo_mysql]
; If mysqlnd is used: Number of cache slots for the internal result set cache
; http://php.net/pdo_mysql.cache_size
pdo_mysql.cache_size = 2000

; Default socket name for local MySQL connects.  If empty, uses the built-in
; MySQL defaults.
; http://php.net/pdo_mysql.default-socket
pdo_mysql.default_socket=
[MySQLi]
mysqli.max_persistent = -1
mysqli.allow_persistent = On
mysqli.max_links = -1
mysqli.cache_size = 2000
mysqli.default_port = 3306
mysqli.reconnect = Off
[mysqlnd]
mysqlnd.collect_statistics = On
mysqlnd.collect_memory_statistics = Off
[Session]
; session.save_handler = files
; session.save_path = "/tmp/sessions"
session.use_cookies = 1