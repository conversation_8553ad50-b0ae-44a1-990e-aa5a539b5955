user  nginx;
pid  /var/run/nginx.pid;
worker_processes        auto;
worker_cpu_affinity     auto;
worker_rlimit_nofile    4096;

events {
    worker_connections  4096;
    multi_accept on;
    use epoll;
}

stream {
    upstream mysql_write {
        server 127.0.0.1:33061;
        # server unix:/cloudsql/long-disk-213608:europe-west9:fra-prod-db;
    }

    upstream mysql_read {
        # hash $remote_addr consistent;
        server 127.0.0.1:33071 max_conns=512;
        # server unix:/cloudsql/long-disk-213608:europe-west9:fra-prod-db-replica max_conns=512;
    }

    server {
        listen 3306;
        proxy_pass mysql_write;
    }

    server {
        listen 3307 so_keepalive=on;
        proxy_pass  mysql_read;
    }
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    charset       utf-8;

    sendfile    on;
    tcp_nopush  on;
    tcp_nodelay on;

    server_tokens off;

    types_hash_max_size     1024;  # Default: 1024;
    types_hash_bucket_size  64; # Default: 64;

    access_log  /var/log/nginx/access.log;

    keepalive_requests          10000; # 和 php-fpm max_children 一樣，多少都不好
    keepalive_timeout           650;
    client_header_timeout       300s;
    client_body_timeout         300s;
    send_timeout                300s;
    reset_timedout_connection   on;

    gzip on;
    gzip_min_length  1000;
    gzip_buffers     4 4k;
    gzip_disable     "MSIE [1-6]\.";

    proxy_set_header        Host $host;
    proxy_set_header        X-Real-IP $remote_addr;
    proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header        Proxy "";

    ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
    ssl_prefer_server_ciphers on;

    upstream php {
        # server unix:/var/www/html/php-fpm.sock;
        server  127.0.0.1:9000;
    }

    # upstream diagnoses {
    #     # server unix:/var/www/html/php-fpm.sock;
    #     server  127.0.0.1:8080;
    # }
    include nginx.server.conf;
}