[global]
daemonize = no
error_log = /proc/self/fd/2
events.mechanism = epoll
pid = /var/run/php-fpm.pid
[www]
user = www-data
group = www-data

; listen = [::]:9000
; listen = /var/www/html/php-fpm.sock
listen = 127.0.0.1:9000
listen.mode = 0666
listen.backlog = 8196
pm = static
pm.max_children = 10

; pm = dynamic
; pm.max_children = 10
; pm.start_servers = 5
; pm.min_spare_servers=2
; pm.max_spare_servers = 5

pm.status_path = /zxcasdqwezxcasdqwezxcasdqwestatus
; access.log = /proc/self/fd/1
; slowlog = /proc/self/fd/2
; request_slowlog_timeout = 5s

; clear_env = no
catch_workers_output = yes
; Redirect worker stdout and stderr into main error log.
; If not set, stdout and stderr will be redirected to
; /dev/null according to FastCGI specs. Default value: no.