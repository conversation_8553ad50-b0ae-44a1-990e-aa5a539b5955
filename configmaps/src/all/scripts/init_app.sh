#!/bin/bash
set -e

echo "🚀 Starting Application Initialization..."

# Navigate to the application directory
cd /var/www/html

# Check if a .env file was provided by ConfigMap/Secret and is present
if [ -f ".env" ]; then
    echo "Found .env file. Caching configuration..."
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
else
    echo "WARNING: .env file not found. Skipping config caching."
fi

# Run database migrations
# The --force flag is recommended for running in production without prompts
echo "Running database migrations..."
php artisan migrate --force

echo "Application initialization complete."

# ------------------------------------------------------------------
# IMPORTANT: Use 'exec' to replace the script process with php-fpm.
# This ensures that php-fpm becomes the main process (PID 1) and
# can correctly receive signals like SIGTERM or SIGINT for graceful shutdowns.
# ------------------------------------------------------------------
echo "Handing over to php-fpm..."
exec php-fpm 