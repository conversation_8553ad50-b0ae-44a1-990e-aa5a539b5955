#!/usr/bin/env bash
# __DIR__="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
__DIR__="/configmaps/"
REPO_NAME=$1
REPO_ENV=$2
REPO_TAG=$3
REPO_VERSION=$4
PROJECT_NAME="long-disk-213608"
BUCKET_NAME="code-${REPO_NAME}"
ZIP_NAME="${REPO_VERSION}.zip"
repo="/var/www/html/repo"

download_repo() {
    php ${__DIR__}/download.php \
        --project="$PROJECT_NAME" \
        --bucket="$BUCKET_NAME" \
        --object="$ZIP_NAME"
    unzip -q /object.zip -d /var/www/html
    rm -f /object.zip
}

download_repo_from_gs() {
    gcloud storage cp "gs://${BUCKET_NAME}/${ZIP_NAME}" /object.zip
    unzip -q /object.zip -d /var/www/html
    rm -f /object.zip
}

gcloud_cli_auth() {
    gcloud auth activate-service-account --key-file=/configmaps/cloud-storage.json
}

renew_repo() {
    rm -rf ${repo}
    mv /var/www/html/${REPO_NAME} ${repo}
}

update_configs() {
    # stderr_to_null="2> /dev/null"
    cp -f ${__DIR__}/php-fpm.conf  /usr/local/etc/php-fpm.conf  ${stderr_to_null}
    cp -f ${__DIR__}/php.ini       /usr/local/etc/php/php.ini   ${stderr_to_null}
    cp -f ${__DIR__}/.env          ${repo}/.env                 ${stderr_to_null}
    cp -f ${__DIR__}/database.php  ${repo}/config/database.php  ${stderr_to_null}
}

npm_install(){
    cd ${repo}
    npm install
    npm run production
}

start_horizen_queue_server(){
    nohup php artisan queue:work --queue=batching &
    nohup php /var/www/html/repo/artisan horizon &
}

main() {
    gcloud_cli_auth
    # download_repo
    download_repo_from_gs
    renew_repo
    update_configs

    # [QTMSW-475] Fix log error
    # touch /var/www/html/repo/storage/logs/laravel.log
    # chown www-data /var/www/html/repo/storage/logs/laravel.log
    # chgrp www-data /var/www/html/repo/storage/logs/laravel.log
    # mkdir -R /var/www/html/repo/storage/logs
    mkdir -p /var/www/html/repo/storage/logs
    # chown -R www-data:www-datga /var/www/html/repo/storage/logs
    chown -R www-data:www-data ${repo}
    chmod 775 /var/www/html/repo/storage/logs
    php -r "opcache_reset();"

    # if [[ $REPO_NAME == 'qtm-api' ]]; then
    #     # for GenerateReport
    #     cd /var/www/html/repo/app/Jobs/GenerateReport && \
    #     gem install bundler && \
    #     bundle install --binstubs .bundle/bin
    # fi

    # 20230613
    # DEBIAN_FRONTEND=noninteractive apt-get update
    # DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends poppler-data
    # rm -r /var/lib/apt/lists/*


    # (QTMSW-794) 避免在 macOS 執行 npm install 後才打包上傳，改成 container 起來後自己去拉
    # npm_install

    cd ${repo}
    php artisan storage:link

    start_horizen_queue_server
    php-fpm
}

main "$@"