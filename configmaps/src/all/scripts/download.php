<?php
    # Includes the autoloader for libraries installed with composer
    require '/vendor/autoload.php';
    putenv('GOOGLE_APPLICATION_CREDENTIALS='. __DIR__ . '/cloud-storage.json');
    
    # Imports the Google Cloud client library
    use Google\Cloud\Storage\StorageClient;
    $options = getopt('', [
        "project:",
        "bucket:",
        "object:"
    ]);
    # Your Google Cloud Platform project ID
    $projectId = $options['project'];
    $bucketName = $options['bucket'];
    $objectPath = $options['object'];

    # Instantiates a client
    $storage = new StorageClient([
        'projectId' => $projectId
    ]);
    # todo dirname(__FILE__) not found
    # /scripts dir
    $stream = $storage
                ->bucket($bucketName)
                ->object($objectPath)
                ->downloadToFile('/object.zip');