#!/usr/bin/env bash
__DIR__="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
repo="/var/www/html/repo"
DB_HOST="127.0.0.1"
DB_NAME="qtm"
DB_USERNAME="root"
DB_PASSWORD="qtmP@ssw0rd"

apt update
apt install -y --no-install-recommends default-mysql-client
rm -r /var/lib/apt/lists/*

# copy static country data
# cp -f ${__DIR__}/Locations_of_QTM_2020-02-21.sql ${repo}/Locations_of_QTM_2020-02-21.sql ${stderr_to_null}
# cp -f ${__DIR__}/cred_europe-fr.sql ${repo}/cred_europe-fr.sql ${stderr_to_null}

# goto root folder
cd "$repo"

# create dtabase if not exists
mysql --host="$DB_HOST" --port="3306" --user="$DB_USERNAME" --password="$DB_PASSWORD" --execute="CREATE DATABASE IF NOT EXISTS $DB_NAME; SHOW DATABASES;"

# wipe out the database 
php artisan db:wipe

# migrate db
php artisan migrate:refresh

# update country table
mysql --host="$DB_HOST" --port="3306" --user="$DB_USERNAME" --password="$DB_PASSWORD" --database="$DB_NAME" --execute="$(cat /configmaps/Locations_of_QTM_2020-02-21.sql)"

# seeder
php artisan db:seed --class=InitDatabaseSeeder

# create credentials
# php artisan passport:client --client --name="QTM APP Client Credentials Grant"
# php artisan passport:client --password --provider=users --name="QTM APP Password Grant"

# load existing credential for existing clusters
cluster_location="fr"
case "${cluster_location}" in
fr)
    mysql --host="$DB_HOST" --port="3306" --user="$DB_USERNAME" --password="$DB_PASSWORD" --database="$DB_NAME" --execute="$(cat /configmaps/cred_europe-fr.sql)"
    ;;
au)
    readonly DB_NAME="au-db-002"
    readonly DB_REGION="australia-southeast1"
    readonly PROJECT_NAME="long-disk-213608"
    ;;
*)
    php artisan passport:client --client --name="QTM APP Client Credentials Grant"
    php artisan passport:client --password --provider=users --name="QTM APP Password Grant"
    ;;
esac

# create personal credential
php artisan passport:client --personal --name="QTM Personal Access Client"
