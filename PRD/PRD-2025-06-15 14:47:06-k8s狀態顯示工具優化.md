# PRD - Kubernetes 狀態顯示工具優化

**建立時間**: 2024-12-19 16:30:00  
**需求標題**: 解決終端機資訊過多導致無法顯示下方資訊的問題  
**優先級**: 中等  

## 問題描述

用戶在使用 `tools/k8s__show_status` 腳本監控 Kubernetes 資源狀態時，遇到以下問題：
- 終端機顯示的資訊行數過多
- 下方的重要資訊無法顯示在螢幕範圍內
- 缺乏換頁機制或滾動功能
- 無法有效查看完整的資源狀態資訊

## 需求分析

### 核心需求
1. **分頁顯示功能**: 能夠分頁查看大量資訊
2. **滾動機制**: 支援上下滾動查看內容
3. **保持原有功能**: 維持原本的 watch 自動更新功能
4. **使用者友善**: 提供清楚的操作說明

### 技術需求
1. 使用 `less` 分頁器支援滾動查看
2. 保留 `kubecolor` 的彩色輸出
3. 提供多種顯示模式選擇
4. 向後相容原有的使用方式

## 解決方案

### 方案一: 增強原有腳本
修改 `tools/k8s__show_status`，新增以下功能：
- `--page`: 使用分頁器顯示（靜態，可滾動）
- `--scroll`: 滾動模式（與 --page 相同，可滾動）
- `--full`: 完整模式（顯示所有欄位和標籤）
- `--help`: 顯示使用說明
- 預設使用簡化的 watch 模式（custom-columns）

### 方案二: 創建進階腳本
新建 `tools/k8s__show_status_advanced`，提供更多選項：
- `--page`: 分頁器模式（推薦）
- `--split`: 分段顯示不同資源類型
- `--compact`: 緊湊模式，只顯示重要資訊
- `--interactive`: 互動模式，可選擇查看特定資源
- `--watch`: 簡化 watch 模式（預設，使用 custom-columns）
- `--full`: 完整 watch 模式（顯示所有欄位和標籤）
- `--auto-scroll`: 自動更新滾動模式（每5秒更新並可滾動）

## 實作細節

### 分頁器功能
- 使用 `less -R` 保持彩色輸出
- 支援常用操作：
  - `q`: 退出
  - 空白鍵: 下一頁
  - `j/k`: 上下移動
  - `g/G`: 移到開頭/結尾
  - `/`: 搜尋功能

### 分段顯示
1. 第一段: 節點和部署狀態
2. 第二段: Pods 和服務
3. 第三段: 存儲和網路資源

### 緊湊模式
- 使用 custom-columns 只顯示關鍵資訊
- 節點: 名稱、狀態、角色、年齡
- 部署: 名稱、就緒數量、可用數量、年齡
- Pods: 僅顯示非 Running 狀態的 Pod

### 簡化顯示 (Custom Columns)
使用 `custom-columns` 只顯示最重要的欄位：
- **節點**: 名稱、狀態、角色、版本、年齡
- **部署**: 名稱、就緒數、更新數、可用數、年齡
- **Pod**: 名稱、就緒狀態、狀態、重啟次數、容器名稱、年齡、節點
- **服務**: 名稱、類型、內部IP、外部IP、端口、年齡
- **Ingress**: 名稱、類別、主機、地址、年齡
- **HPA**: 名稱、目標、CPU使用率、最小/最大Pod數、當前副本數、年齡

### 互動模式
提供選單讓使用者選擇要查看的資源類型：
1. 節點 (Nodes)
2. 部署 (Deployments)  
3. Pods
4. 服務 (Services)
5. 存儲 (PVC)
6. Ingress
7. 憑證 (ManagedCertificate)
8. HPA
9. 全部資源

## 使用方式

### 基本腳本
```bash
# 預設 watch 模式
./tools/k8s__show_status

# 分頁模式
./tools/k8s__show_status --page

# 滾動模式（與 --page 相同）
./tools/k8s__show_status --scroll

# 完整模式（原始版本）
./tools/k8s__show_status --full

# 顯示說明
./tools/k8s__show_status --help
```

### 進階腳本
```bash
# 分頁模式（推薦）
./tools/k8s__show_status_advanced --page

# 分段顯示
./tools/k8s__show_status_advanced --split

# 緊湊模式
./tools/k8s__show_status_advanced --compact

# 互動模式
./tools/k8s__show_status_advanced --interactive

# 自動更新滾動模式
./tools/k8s__show_status_advanced --auto-scroll

# 完整 watch 模式
./tools/k8s__show_status_advanced --full

# 顯示說明
./tools/k8s__show_status_advanced --help
```

## 預期效果

1. **解決資訊過多問題**: 透過分頁和滾動機制，使用者可以完整查看所有資訊
2. **提升使用體驗**: 提供多種顯示模式，適應不同使用場景
3. **保持相容性**: 原有的使用方式仍然有效
4. **增加靈活性**: 使用者可根據需要選擇最適合的顯示模式

## 技術實作狀態

- ✅ 修改原有 `k8s__show_status` 腳本
- ✅ 創建進階 `k8s__show_status_advanced` 腳本  
- ✅ 設定執行權限
- ✅ 提供完整的使用說明和操作指南
- ✅ 使用 custom-columns 簡化顯示欄位
- ✅ 新增完整模式選項保留原始功能

## 後續優化建議

1. 可考慮加入配置文件，讓使用者自訂顯示的資源類型
2. 增加過濾功能，例如只顯示特定 namespace 的資源
3. 加入匯出功能，將狀態資訊儲存到檔案
4. 考慮整合到現有的監控工具鏈中

---

**完成時間**: 2024-12-19 16:30:00  
**實作者**: AI Assistant  
**狀態**: 已完成實作 