# 需求：修改 Cron Deployment 使用 TCP 連接方式

## 問題描述

用戶希望 php-cron pod 能夠像 app deployment 一樣使用 `127.0.0.1` TCP 連接方式連接 CloudSQL，而不是使用 Unix socket。

## 問題分析

當前的 cron deployment 配置：
1. **CloudSQL Proxy 配置**：使用 `--unix-socket=/cloudsql` 強制只提供 Unix socket 連接
2. **實例配置**：`{{INSTANCES}}` 變數只包含實例名稱，沒有 TCP 連接配置
3. **資料庫配置**：使用 `DB_HOST=mysql` 而不是 `127.0.0.1`

## 解決方案

### 1. 修改 CloudSQL Proxy 實例配置

在 `cloudbuild.yaml` 中修改 cron deployment 的實例配置：

```bash
# 修改前
w_1="${project_id}:${db_region}:${db_name}"
r_1="${project_id}:${db_region}:${db_name}-replica"

# 修改後
w_1="${project_id}:${db_region}:${db_name}=tcp:3306"
r_1="${project_id}:${db_region}:${db_name}-replica=tcp:3307"
```

### 2. 修改 Cron Deployment 模板

在 `k8s-yaml/bundle/bundle.cron.template.yaml` 中：

**移除 Unix socket 參數：**
```yaml
# 修改前
command:
  - "/cloud-sql-proxy"
  - "--unix-socket=/cloudsql"
  - "--credentials-file=/configmaps/proxyuser.json"
  - "{{INSTANCES}}"

# 修改後
command:
  - "/cloud-sql-proxy"
  - "--credentials-file=/configmaps/proxyuser.json"
  - "{{INSTANCES}}"
```

**修改 liveness probe：**
```yaml
# 修改前
livenessProbe:
  exec:
    command:
      - "/bin/bash"
      - "-c"
      - 'test -S /cloudsql/$(echo {{INSTANCES}} | cut -d "=" -f 1)'

# 修改後
livenessProbe:
  exec:
    command:
      - "/bin/bash"
      - "-c"
      - "nc -z 127.0.0.1 3306"
```

### 3. 修改資料庫配置

**在 `configmaps/src/repos/qtm-api/database.php` 中：**
```php
// 修改前
'write' => [
    'host' => env('DB_HOST_WRITE', '127.0.0.1'),
    'port' => env('DB_PORT_WRITE', '3306')
],

// 修改後
'write' => [
    'host' => '127.0.0.1',
    'port' => '3306'
],
```

**在所有環境檔案中：**
```
# 修改前
DB_HOST=mysql

# 修改後
DB_HOST=127.0.0.1
```

### 4. CloudSQL 等待邏輯

`init_cron.sh` 中的 `wait_for_cloudsql()` 函數已經正確檢查 `127.0.0.1:3306`，無需修改。

## 技術優勢

✅ **與 App Deployment 一致** - 使用相同的 TCP 連接方式  
✅ **更好的相容性** - TCP 連接更通用，支援更多工具  
✅ **簡化配置** - 統一的連接方式，減少配置複雜度  
✅ **更好的監控** - TCP 連接更容易監控和調試  

## 部署影響

- **需要重新部署** cron deployment 以應用新的 CloudSQL Proxy 配置
- **向下相容** - 不影響現有的 app deployment
- **配置統一** - 所有環境使用相同的連接邏輯

## 測試驗證

部署後驗證：
1. CloudSQL Proxy 在 `127.0.0.1:3306/3307` 上監聽
2. `wait_for_cloudsql()` 函數成功檢測到連接
3. `php artisan horizon` 和 `php artisan queue:work` 正常啟動
4. 資料庫連接正常，無連接錯誤

---

**實施時間：** 2025-06-13  
**影響範圍：** Cron Deployment  
**風險等級：** 低 - 只影響 cron deployment，不影響主要服務 