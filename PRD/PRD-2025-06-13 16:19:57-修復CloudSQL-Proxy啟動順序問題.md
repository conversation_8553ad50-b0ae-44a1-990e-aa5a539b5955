# 需求：修復 CloudSQL Proxy 啟動順序問題

## 問題描述

使用者反映即使恢復為 TCP 連接方式（`127.0.0.1:3306/3307`），`php-cron` pod 仍然遇到資料庫無法連線的問題。

## 根本原因分析

這是一個典型的**容器啟動順序問題**：

1. **並行啟動**：在 Kubernetes 中，同一個 Pod 內的多個容器是並行啟動的
2. **時序競爭**：`php-cron` 容器可能在 `cloudsql` 容器完全啟動並建立 TCP 監聽之前就嘗試連接資料庫
3. **連接失敗**：導致 `php artisan queue:work` 和 `php artisan horizon` 因為無法連接資料庫而退出

## 解決方案

在 `init_cron.sh` 腳本中添加 CloudSQL Proxy 就緒檢查：

### 1. 新增 `wait_for_cloudsql()` 函數

```bash
wait_for_cloudsql() {
    echo "等待 CloudSQL Proxy 啟動..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z 127.0.0.1 3306 2>/dev/null; then
            echo "CloudSQL Proxy 已啟動 (port 3306)"
            return 0
        fi
        echo "嘗試 $attempt/$max_attempts: CloudSQL Proxy 尚未就緒，等待 2 秒..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "警告: CloudSQL Proxy 在 60 秒內未啟動，繼續執行..."
    return 1
}
```

### 2. 在啟動隊列 worker 之前調用

```bash
# Wait for CloudSQL Proxy to be ready before starting queue workers
wait_for_cloudsql

# Start horizon in background
php artisan horizon > ${repo}/storage/logs/horizon.log 2>&1 &

# Start batching queue worker
php artisan queue:work --queue=batching > ${repo}/storage/logs/queue_batching.log 2>&1 &
```

## 技術細節

- **檢查方式**：使用 `nc -z 127.0.0.1 3306` 檢查 CloudSQL Proxy 的 TCP 監聽是否就緒
- **重試機制**：最多嘗試 30 次，每次間隔 2 秒（總共 60 秒超時）
- **容錯處理**：即使超時也會繼續執行，避免完全阻塞啟動流程
- **日誌輸出**：提供清晰的等待狀態訊息，便於除錯

## 預期結果

- CloudSQL Proxy 完全啟動後才會啟動隊列 worker
- 避免因啟動順序導致的資料庫連線錯誤
- `php artisan horizon` 和 `php artisan queue:work --queue=batching` 能穩定運行
- 保持與正常 php-cron pod 相同的 TCP 連接方式 