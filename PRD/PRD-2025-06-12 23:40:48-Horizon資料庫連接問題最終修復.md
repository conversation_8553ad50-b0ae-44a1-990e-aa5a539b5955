# PRD-2025-06-12 23:40:48-Horizon資料庫連接問題最終修復

## 專案背景

用戶回報在執行 `cloud-deploy.sh sta develop` 部署後，Laravel Horizon queue server 在 cron 容器中啟動失敗，出現資料庫連接錯誤。

## 問題分析

### 根本原因發現
經過深入分析，發現了容器架構的根本性差異：

**主應用 Pod 架構**：
- `php-fpm` 容器（Laravel 應用）
- `nginx` 容器（反向代理 + MySQL TCP 代理）
  - 開放端口：80, 443, 3306, 3307
  - 提供 MySQL TCP 代理：`127.0.0.1:3306/3307` → CloudSQL Unix Socket
- `cloudsql` 容器（CloudSQL 代理）

**Cron Pod 架構**：
- `php-cron` 容器（Laravel Horizon + Cron）
- `cloudsql` 容器（CloudSQL 代理）
  - 開放端口：33061, 33071
  - 只提供 Unix Socket：`/cloudsql/long-disk-213608:us-central1:sta-db-003`

### 配置錯誤
兩個 Pod 的 Laravel 配置都錯誤地使用了 `DB_HOST=mysql`，但實際上：
1. 沒有名為 `mysql` 的 Kubernetes Service
2. 主應用容器能工作是因為 nginx 在 localhost:3306 提供代理
3. Cron 容器沒有 nginx，所以連接 `mysql` 主機失敗

## 解決方案

### 技術實現

1. **修改 cron 容器資料庫配置**：
   ```bash
   # 在 init_cron.sh 的 update_configs() 函數中
   # 動態修改 .env 檔案使用 Unix Socket
   sed -i "s|DB_HOST=.*|DB_HOST=localhost|g" ${repo}/.env
   sed -i "s|DB_PORT=.*|DB_PORT=|g" ${repo}/.env
   echo "" >> ${repo}/.env
   echo "DB_SOCKET=$WRITE_SOCKET" >> ${repo}/.env
   ```

2. **自動偵測 CloudSQL Socket**：
   ```bash
   for socket in /cloudsql/*; do
       if [[ -S "$socket" && "$socket" != *"-replica" ]]; then
           WRITE_SOCKET="$socket"
       elif [[ -S "$socket" && "$socket" == *"-replica" ]]; then
           READ_SOCKET="$socket"
       fi
   done
   ```

3. **移除複雜的代理邏輯**：
   - 刪除 socat TCP 代理設置
   - 簡化為直接使用 Unix Socket 連接

### 配置變更

**修改前（錯誤）**：
```
DB_HOST=mysql
DB_PORT=3306
```

**修改後（正確）**：
```
DB_HOST=localhost
DB_PORT=
DB_SOCKET=/cloudsql/long-disk-213608:us-central1:sta-db-003
```

## 技術細節

### 文件修改
- `configmaps/src/all/scripts/init_cron.sh`
  - 新增 Unix Socket 偵測和配置邏輯
  - 移除複雜的 socat 代理設置
  - 改善錯誤處理和日誌記錄

### 解決的問題
1. **.env 格式錯誤**：修復配置追加時的換行問題
2. **資料庫連接方式**：從錯誤的 TCP 連接改為正確的 Unix Socket
3. **容器權限限制**：避免需要額外權限的 socat 安裝
4. **架構不一致**：讓 cron 容器使用適合其架構的連接方式

## 驗證步驟

### 部署驗證
```bash
./tools/cloud-deploy qtm-api sta develop
```

### 功能驗證
```bash
# 檢查 cron pod 狀態
kubectl get pods | grep cron

# 檢查 Horizon 進程
kubectl exec <cron-pod> -c php-cron -- ps aux | grep horizon

# 測試資料庫連接
kubectl exec <cron-pod> -c php-cron -- bash -c "cd /var/www/html/repo && php artisan migrate:status"
```

## 預期結果

1. **Cron 容器正常啟動**：2/2 Running 狀態
2. **Horizon 正常運行**：多個 worker 進程處理 queue
3. **資料庫連接正常**：能夠執行 artisan 命令
4. **Queue 任務處理**：能夠正常處理背景任務

## 架構優化

這次修復澄清了不同容器的正確連接方式：

- **主應用容器**：使用 nginx 提供的 TCP 代理（`127.0.0.1:3306`）
- **Cron 容器**：直接使用 CloudSQL Unix Socket
- **未來新容器**：根據容器內可用的代理服務選擇適當的連接方式

## 監控建議

1. **Horizon 狀態監控**：定期檢查 Horizon worker 進程
2. **Queue 處理監控**：監控 queue 任務的處理速度和失敗率
3. **資料庫連接監控**：監控 CloudSQL 連接狀態
4. **容器健康檢查**：確保 cron 容器持續正常運行

## 總結

通過深入分析容器架構差異，我們發現了配置不匹配的根本原因，並實施了針對性的解決方案。這次修復不僅解決了 Horizon 啟動問題，也為未來的容器配置提供了清晰的指導原則。

**關鍵學習**：不同的容器架構需要不同的資料庫連接策略，不能一刀切地使用相同配置。 