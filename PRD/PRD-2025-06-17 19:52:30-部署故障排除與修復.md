# PRD-2025-06-17 19:52:30-部署故障排除與修復

## 問題背景

在執行 sta 環境部署時遇到了 Step #11 "Wait for Deployment and Switch Service" 失敗，雖然 Pod 都正常運行（Ready=2, Desired=2），但服務狀態顯示 `Available: False`，導致部署流程無法完成。

## 錯誤分析

### 主要錯誤

1. **Ingress IP 衝突**
   ```
   Invalid value for field 'resource.IPAddress': '**************'. 
   Specified IP address is in-use and would result in a conflict.
   ```

2. **SSL 憑證缺失**
   ```
   ManagedCertificate default:sta-google-managed-certificate missing
   ```

3. **舊版本 HPA 殘留**
   ```
   FailedGetScale horizontalpodautoscaler/app-v20250605121624 
   deployments/scale.apps "app-v20250605121624" not found
   ```

### 根本原因分析

- **服務指向錯誤**：服務標籤指向舊版本 `v20250617161328`，而非最新版本 `v20250617193108`
- **資源清理不完整**：舊版本的 HPA 和 deployment 沒有被正確清理
- **SSL 憑證缺失**：`sta-google-managed-certificate` 被意外刪除

## 解決方案實施

### 1. 環境檢查
```bash
# 連接到正確的 sta 集群
gcloud container clusters get-credentials sta-qtm-api --zone=us-central1-c --project=long-disk-213608
```

### 2. 清理舊版本 HPA
```bash
# 刪除殘留的舊版本 HPA
kubectl delete hpa app-v20250605121624
kubectl delete hpa app-v20250617161328
```

### 3. 重建 SSL 憑證
```bash
# 重新創建 SSL 憑證
kubectl apply -f k8s-yaml/ssl-google-managed/sta-ssl-google-managed.yaml
```

### 4. 手動切換服務
```bash
# 將服務切換到最新版本
kubectl patch service app -p '{"spec":{"selector":{"env":"sta","version":"v20250617193108"}}}'
```

### 5. 清理舊版本部署
```bash
# 刪除舊版本的 deployment
kubectl delete deployment app-v20250617161328 cron-v20250617161328
```

## 修復結果驗證

### 服務狀態
- **Pod 狀態**: 2/2 Running ✅
- **服務外部 IP**: ************* ✅
- **健康檢查**: `{"status":"ok","database":"connected"}` ✅

### SSL 憑證狀態
```yaml
status:
  certificateStatus: Provisioning
  domainStatus:
  - domain: sta-dashboard.qtmedical.com
    status: Provisioning
  - domain: sta-qtm-api.qtmedical.com
    status: Provisioning
```

## 預防措施

### 1. 自動化清理腳本
建議在部署腳本中加入舊版本資源的自動清理機制：
```bash
# 清理 N-2 版本之前的資源
kubectl delete deployment,hpa -l env=sta --field-selector metadata.creationTimestamp<$(date -d '2 versions ago' --iso-8601)
```

### 2. SSL 憑證監控
建議加入 SSL 憑證狀態的檢查：
```bash
# 檢查 SSL 憑證是否存在
kubectl get managedcertificate sta-google-managed-certificate || kubectl apply -f k8s-yaml/ssl-google-managed/sta-ssl-google-managed.yaml
```

### 3. 服務切換驗證
建議在切換服務前驗證目標版本的健康狀態：
```bash
# 驗證目標版本的 Pod 都處於 Ready 狀態
kubectl wait --for=condition=ready pod -l version=v${NEW_VERSION} --timeout=300s
```

## 改進建議

### 1. 部署流程優化
- 在服務切換前加入更詳細的健康檢查
- 增加 SSL 憑證存在性檢查
- 改進舊版本資源的清理邏輯

### 2. 監控增強
- 加入 SSL 憑證狀態監控
- 增加服務切換失敗的告警機制
- 監控舊版本資源的清理狀況

### 3. 文檔更新
- 更新故障排除手冊
- 建立服務切換的標準操作程序
- 記錄常見問題和解決方案

## 結論

此次故障主要由於自動化部署腳本中的服務切換邏輯不夠健全，以及 SSL 憑證的意外缺失造成。通過手動介入修復了所有問題，服務已恢復正常。建議改進自動化部署流程，加強資源清理和狀態檢查機制。 