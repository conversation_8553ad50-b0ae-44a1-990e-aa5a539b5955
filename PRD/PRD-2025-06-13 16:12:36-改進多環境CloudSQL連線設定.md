# 需求：改進多環境 CloudSQL 連線設定

## 問題描述

使用者指出之前的解決方案有問題：將特定環境的 CloudSQL 實例名稱硬編碼到 `database.php` 中，這樣其他環境（fra、aus、pro）就無法使用了。

## 問題分析

之前的方案：
```php
'unix_socket' => '/cloudsql/long-disk-213608:us-central1:sta-db-003',
```

這種硬編碼方式只適用於 staging 環境，無法支援多環境部署。

## 改進方案

### 1. 修改 `database.php` 使用環境變數

將硬編碼的 CloudSQL 實例名稱改為使用環境變數：

```php
'write' => [
    'unix_socket' => env('DB_SOCKET_WRITE', '/cloudsql/' . env('CLOUDSQL_INSTANCE_WRITE')),
],
'read' => [
    'unix_socket' => env('DB_SOCKET_READ', '/cloudsql/' . env('CLOUDSQL_INSTANCE_READ')),
],
```

### 2. 為每個環境檔案添加相應的 CloudSQL 實例變數

**env.sta (Staging):**
```
CLOUDSQL_INSTANCE_WRITE=long-disk-213608:us-central1:sta-db-003
CLOUDSQL_INSTANCE_READ=long-disk-213608:us-central1:sta-db-003-replica
```

**env.pro (Production):**
```
CLOUDSQL_INSTANCE_WRITE=long-disk-213608:us-central1:pro-db-003
CLOUDSQL_INSTANCE_READ=long-disk-213608:us-central1:pro-db-003-replica
```

**env.fra (France):**
```
CLOUDSQL_INSTANCE_WRITE=long-disk-213608:europe-west9:fra-db-003
CLOUDSQL_INSTANCE_READ=long-disk-213608:europe-west9:fra-db-003-replica
```

**env.aus (Australia):**
```
CLOUDSQL_INSTANCE_WRITE=long-disk-213608:australia-southeast1:aus-db-003
CLOUDSQL_INSTANCE_READ=long-disk-213608:australia-southeast1:aus-db-003-replica
```

### 3. 恢復 `init_cron.sh` 中的環境檔案選擇邏輯

```bash
cp -f ${__DIR__}/env.${REPO_ENV}  ${repo}/.env
```

這樣腳本會根據 `REPO_ENV` 參數自動選擇正確的環境檔案。

## 技術優勢

1. **多環境支援**：每個環境都有自己的 CloudSQL 實例設定
2. **動態配置**：`database.php` 通過環境變數動態決定連接參數
3. **維護性**：新增環境時只需要添加對應的環境檔案
4. **一致性**：所有環境使用相同的 `database.php` 設定邏輯

## 預期結果

- 所有環境（sta、pro、fra、aus）都能正確連接到各自的 CloudSQL 實例
- `php artisan queue:work --queue=batching` 在所有環境中都能正常運行
- 部署流程保持一致，無需為不同環境修改代碼 