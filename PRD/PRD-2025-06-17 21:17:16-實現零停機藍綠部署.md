# 實現零停機藍綠部署

## 需求背景

用戶在使用 `cloud-deploy qtm-api sta develop` 部署時，發現在新版本切換過程中會有服務中斷的問題。經過每秒監控，發現在 Step #11 執行流量切換時出現 502 錯誤：

```
Step #11 - "Wait for Deployment and Switch Service": 📊 Pod 就緒狀態: 1/1 個 Pod 已就緒
Step #11 - "Wait for Deployment and Switch Service": ✅ 至少有一個 Pod 已就緒，繼續部署流程...
Step #11 - "Wait for Deployment and Switch Service": ✅ 所有新 Pod 健康檢查通過
Step #11 - "Wait for Deployment and Switch Service": 📋 Phase 3: 執行流量切換...
Step #11 - "Wait for Deployment and Switch Service": 🔄 切換服務流量到新版本...
Step #11 - "Wait for Deployment and Switch Service": service/app patched
Step #11 - "Wait for Deployment and Switch Service": service/app-ipv6 patched
Step #11 - "Wait for Deployment and Switch Service": ⏳ 等待流量切換生效...
```

## 問題分析

當前部署流程的問題在於：

1. **立即斷開舊 Pod 的連接** - 使用 `kubectl patch` 直接修改服務選擇器會立即中斷所有現有連接
2. **新 Pod 可能未完全準備好** - 雖然 Pod 是 Ready 狀態，但應用程式可能還需要時間初始化
3. **沒有漸進式切換** - 流量是一次性全部切換，而不是逐步遷移

## 解決方案

### 方案一：改進現有流程（快速修復）

創建了 `tools/k8s_wait_and_switch_v2.sh` 腳本，實現零停機部署：

#### 主要改進：

1. **雙版本共存期**
   - 先使用通用選擇器 `{"app": "qtm-api"}` 讓新舊版本同時接收流量
   - 等待負載均衡器更新（30秒）

2. **逐步縮減舊版本**
   - 逐個減少舊版本的副本數
   - 每次縮減後等待 20 秒
   - 監控新版本健康狀態

3. **增強的健康檢查**
   - 檢查每個 Pod 的 Ready 狀態
   - 檢查所有容器是否運行
   - 執行應用層健康檢查

4. **預熱階段**
   - 對每個新 Pod 發送測試請求
   - 額外等待 20 秒確保應用完全啟動

#### 修改 cloudbuild.yaml：

```yaml
# 11. 等待部署就緒並切換服務（零停機）
- name: 'gcr.io/cloud-builders/gcloud'
  id: 'Wait for Deployment and Switch Service'
  entrypoint: 'bash'
  env:
    - 'VERSION_NEXT=${_VERSION_NEXT}'
  args:
    - '-c'
    - |
      # 獲取 kubeconfig
      gcloud container clusters get-credentials ${_CLUSTER_NAME} --zone=${_COMPUTE_ZONE} --project=${_PROJECT_ID}
      
      # 執行零停機等待和切換腳本
      chmod +x /workspace/tools/k8s_wait_and_switch_v2.sh
      /workspace/tools/k8s_wait_and_switch_v2.sh
```

### 方案二：完整的藍綠部署工具

創建了 `tools/blue-green-deploy` 工具，提供更完整的藍綠部署功能：

#### 功能特性：

1. **部署命令**
   - 支援零停機藍綠部署
   - 可配置預熱時間
   - 支援金絲雀部署（需要服務網格）

2. **回滾功能**
   - 快速回滾到上一個版本
   - 自動查找可用的歷史版本

3. **狀態檢查**
   - 查看當前服務版本
   - 列出所有部署和 Pod 狀態
   - 顯示 HPA 配置

4. **驗證功能**
   - 檢查環境配置
   - 驗證 kubectl 和集群連接
   - 確認必要的工具安裝

#### 使用範例：

```bash
# 執行部署
./tools/blue-green-deploy deploy --version=v20240112123456 --env=sta

# 查看狀態
./tools/blue-green-deploy status --env=sta

# 執行回滾
./tools/blue-green-deploy rollback --env=sta

# 驗證環境
./tools/blue-green-deploy validate
```

## 實施細節

### 零停機切換流程：

1. **階段一：等待新部署就緒**
   - 檢查 Deployment 存在
   - 等待所有副本就緒
   - 超時設置：5 分鐘

2. **階段二：健康檢查和預熱**
   - 獲取所有新 Pod 信息
   - 檢查 Pod Ready 狀態
   - 檢查容器運行狀態
   - 執行應用層健康檢查
   - 預熱新 Pod

3. **階段三：實施零停機流量切換**
   - 創建臨時雙版本服務選擇器
   - 新舊版本同時接收流量
   - 逐步縮減舊版本副本
   - 監控新版本健康狀態

4. **階段四：最終驗證**
   - 切換到新版本專屬選擇器
   - 執行服務端點驗證
   - 重試機制（最多 3 次）

5. **階段五：清理舊版本**
   - 刪除舊版本 Deployment
   - 刪除舊版本 HPA
   - 刪除舊版本 Cron 部署

## 關鍵技術點

1. **服務選擇器策略**
   - 使用通用標籤實現雙版本共存
   - 避免直接切換造成的連接中斷

2. **漸進式遷移**
   - 逐步減少舊版本副本數
   - 給予足夠的時間讓連接自然結束

3. **健康監控**
   - 多層次健康檢查
   - 實時監控切換過程

4. **回滾能力**
   - 保留歷史版本
   - 快速回滾機制

## 預期效果

1. **零停機時間** - 服務在切換過程中持續可用
2. **平滑過渡** - 流量逐步從舊版本遷移到新版本
3. **降低風險** - 可以快速回滾到上一個版本
4. **提升可靠性** - 完善的健康檢查和驗證機制

## 後續優化建議

1. **實施真正的金絲雀部署**
   - 整合 Istio 或其他服務網格
   - 支援基於百分比的流量分配

2. **增加監控和告警**
   - 整合 Prometheus 監控
   - 設置部署過程告警

3. **自動化回滾**
   - 基於錯誤率自動回滾
   - 整合 CI/CD 流程

4. **多區域部署支援**
   - 支援跨區域的漸進式部署
   - 區域級別的健康檢查 