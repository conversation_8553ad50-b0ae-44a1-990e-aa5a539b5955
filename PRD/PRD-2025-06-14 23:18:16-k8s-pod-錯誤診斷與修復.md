# PRD - K8s Pod 錯誤診斷與修復

**時間：** 2025-06-14 23:18:16  
**需求：** 診斷並修復 Pod `app-v20250614230518-**********-hzpbv` 的錯誤問題

## 問題描述

用戶回報 Kubernetes Pod `app-v20250614230518-**********-hzpbv` 出現錯誤，需要進行診斷和修復。

## 診斷過程

### 1. Pod 狀態檢查
- 使用 `kubectl describe pod` 檢查 Pod 詳細狀態
- 發現 Pod 狀態為 `Terminating`
- 識別出這是由於 GKE cluster-autoscaler 進行節點縮容導致的正常終止

### 2. 日誌分析
- 檢查 php-fpm 容器日誌，發現 OPcache 配置警告
- 檢查 nginx 容器日誌
- 分析事件日誌，確認資源調度問題

### 3. 根本原因分析
- **主要問題：** Pod 正在被 cluster-autoscaler 終止（非錯誤）
- **次要問題：** PHP OPcache 配置使用了已棄用的 `opcache.fast_shutdown` 設定
- **資源問題：** 初始調度時遇到記憶體不足，觸發自動擴容

## 解決方案

### 1. PHP 配置修復
修正 `configmaps/src/all/configs/php.ini` 中的 OPcache 設定：
```ini
; opcache.fast_shutdown=1 ; 已棄用，移除此設定
```

### 2. 集群狀態確認
- 確認新的 Pod 已正常運行
- 檢查節點資源使用情況，確保有足夠資源

### 3. 監控建議
- 持續監控 Pod 健康狀態
- 關注集群自動擴縮容行為
- 定期檢查 PHP 錯誤日誌

## 技術細節

### Pod 資源配置
- php-fpm: CPU 1-2 cores, Memory 2-4Gi
- nginx: CPU 100m-500m, Memory 128Mi-512Mi
- cloudsql: CPU 100m-500m, Memory 128Mi-512Mi

### 健康檢查配置
- Liveness probe: TCP socket :9000 (php-fpm)
- Readiness probe: HTTP GET /api/healthz (nginx)

## 結論

此次問題主要是由於 GKE 集群的正常縮容操作導致的 Pod 終止，並非實際的應用程式錯誤。同時修復了 PHP OPcache 的配置警告，提升了系統的穩定性。

## 完整檢查結果

### 1. Pod 狀態
- **舊版本 Pod:** `app-v20250613233713-5495869c65-sfp52` (1/1) - 正在被終止
- **新版本 Pod:** 
  - `app-v20250614230518-**********-2qcjd` (3/3) - 正常運行
  - `app-v20250614230518-**********-bw7sx` (3/3) - 正常運行

### 2. Service 和 Ingress
- **LoadBalancer Service:** `app` - 正常運行，外部 IP: *************
- **Ingress:** `sta-ingress-with-tls-https` - 有 SSL 憑證問題警告，但不影響服務
- **健康檢查:** `/api/healthz` 回應正常 (HTTP 200)

### 3. 資源使用情況
- **節點資源:** 記憶體使用率 23%-69%，CPU 使用率 1%-7%
- **Pod 資源:** 
  - CPU: 44m-205m (正常範圍)
  - Memory: 1150Mi-1398Mi (在 2-4Gi 限制內)

### 4. 發現的配置問題
- **PHP OPcache:** 在多個配置檔案中發現 `opcache.fast_shutdown=1` 已棄用設定
- **修正檔案:**
  - `configmaps/src/all/configs/php.ini`
  - `configmaps/src/all/configs/php-au-fra.ini`
  - `configmaps/src/all/configs/php-pro.ini`
  - `configmaps/src/all/configs/php-fpm.conf`

### 5. 效能問題
- **慢查詢:** 發現部分請求執行時間超過 10 秒閾值
- **建議:** 考慮優化資料庫查詢或調整 `request_slowlog_timeout` 設定

## 後續行動

1. **立即行動:**
   - 部署修正後的 PHP 配置
   - 重新建置並部署新的 ConfigMap

2. **監控項目:**
   - 監控新 Pod 的 PHP 錯誤日誌
   - 關注慢查詢警告的頻率
   - 檢查 SSL 憑證問題是否影響服務

3. **優化建議:**
   - 分析慢查詢的根本原因
   - 考慮調整 PHP-FPM 進程池配置
   - 檢查資料庫連線池設定 