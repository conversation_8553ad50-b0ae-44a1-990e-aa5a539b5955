# PRD - 修改 k8s 狀態腳本 AGE 欄位顯示

**建立時間：** 2025-06-16 22:50:50  
**需求標題：** 修改 Kubernetes 狀態顯示腳本的 AGE 欄位格式

## 需求描述

用戶希望將 `tools/k8s__show_status` 腳本中的 AGE 欄位從顯示絕對時間戳改為顯示相對時間（如 "2h3m"、"5d"），讓時間資訊更容易閱讀和理解。

## 問題分析

原本的腳本使用 `custom-columns` 格式來顯示 `.metadata.creationTimestamp`，這會顯示完整的時間戳（如 "2024-01-15T10:30:00Z"），對用戶來說不夠直觀。

## 解決方案

將所有使用 `custom-columns` 的 kubectl 命令改為使用預設格式或 `-o wide` 格式：

### 修改內容

1. **節點狀態**：從 `custom-columns` 改為 `get nodes`
2. **部署狀態**：從 `custom-columns` 改為 `get deploy`  
3. **Pod 狀態**：從 `custom-columns` 改為 `get pods -o wide`
4. **服務狀態**：從 `custom-columns` 改為 `get svc`
5. **Ingress 狀態**：從 `custom-columns` 改為 `get ing`
6. **HPA 狀態**：從 `custom-columns` 改為 `get hpa`

### 影響範圍

- `--page` 模式
- `--scroll` 模式  
- 預設 watch 模式

## 預期效果

AGE 欄位將顯示：
- `2h3m` 表示 2 小時 3 分鐘前
- `5d` 表示 5 天前
- `1m30s` 表示 1 分鐘 30 秒前

這樣的格式更符合用戶的閱讀習慣，能快速了解資源的存在時間。

## 實作狀態

✅ 已完成修改 `tools/k8s__show_status` 腳本
✅ 所有模式（page、scroll、watch）都已更新
✅ 保持原有功能不變，僅改善 AGE 欄位顯示格式

## 技術細節

### 修改前
```bash
kubecolor --force-colors get nodes -o custom-columns='NAME:.metadata.name,STATUS:.status.conditions[?(@.type=="Ready")].status,ROLES:.metadata.labels.kubernetes\.io/role,VERSION:.status.nodeInfo.kubeletVersion,AGE:.metadata.creationTimestamp'
```

### 修改後
```bash
kubecolor --force-colors get nodes
```

kubectl 的預設輸出格式會自動將 AGE 欄位顯示為相對時間，這是最簡潔且有效的解決方案。

---

# 額外需求：替換 cloudbuild.yaml 中的 k8s-deploy-tools image

**發現時間：** 2025-06-16 22:50:50  
**問題：** cloudbuild.yaml 中使用了不存在的 `asia-east1-docker.pkg.dev/${_PROJECT_ID}/k8s-deploy/k8s-deploy-tools:latest` image

## 問題分析

在 `cloudbuild.yaml` 中多處使用了 `k8s-deploy-tools:latest` image，但：
1. 沒有找到建構這個 image 的 Dockerfile
2. 沒有建構步驟來建立這個 image
3. 這個 image 主要用於：壓縮代碼、上傳 GCS、更新 ConfigMaps、Kubernetes 操作

## 解決方案：使用 Google Cloud 標準建構工具

### Google Cloud 標準建構工具說明：

1. **`gcr.io/cloud-builders/gcloud`** - 包含 gcloud, gsutil, kubectl, docker
2. **`gcr.io/cloud-builders/docker`** - Docker 操作
3. **`ubuntu`** - 基本 Linux 環境，可安裝額外工具

### 替換策略：

- **壓縮代碼步驟**：使用 `ubuntu` + zip 工具
- **上傳 GCS 步驟**：使用 `gcr.io/cloud-builders/gcloud`
- **ConfigMaps 步驟**：使用 `gcr.io/cloud-builders/gcloud` + envsubst

## 實作狀態

✅ 已完成：修改 cloudbuild.yaml 中的 image 引用

### 具體修改內容：

1. **步驟 5 - 壓縮代碼**：`k8s-deploy-tools` → `ubuntu` (並添加 zip 工具安裝)
2. **步驟 6 - 上傳到 GCS**：`k8s-deploy-tools` → `gcr.io/cloud-builders/gcloud`
3. **步驟 7 - 更新 ConfigMaps**：`k8s-deploy-tools` → `gcr.io/cloud-builders/gcloud`
4. **步驟 8 - 更新和部署應用 YAML**：`k8s-deploy-tools` → `gcr.io/cloud-builders/gcloud`
5. **步驟 9 - 更新和部署 Cron YAML**：`k8s-deploy-tools` → `gcr.io/cloud-builders/gcloud`
6. **步驟 10 - 更新和應用 HPA**：`k8s-deploy-tools` → `gcr.io/cloud-builders/gcloud`
7. **步驟 11 - 等待部署就緒並切換服務**：`k8s-deploy-tools` → `gcr.io/cloud-builders/gcloud`

### 優點：

- 使用 Google Cloud 官方維護的標準建構工具
- 無需自行維護 Docker image
- 更好的穩定性和安全性
- 自動更新和修補程式支援 