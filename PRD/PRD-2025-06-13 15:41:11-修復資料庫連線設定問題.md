# 需求：修復 php-cron pod 資料庫連線設定問題

## 問題描述

在修復了 `init_cron.sh` 腳本中的環境檔案路徑問題後，`php artisan queue:work --queue=batching` 程序仍然無法正常運行，出現資料庫連線錯誤：

```
SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: ...)
PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at mysql:host=127....
```

## 根本原因分析

經過深入調查發現：

1. **環境檔案問題已解決**：`.env` 檔案現在能正確複製到應用程式目錄
2. **資料庫設定檔案問題**：`configmaps/src/repos/qtm-api/database.php` 中硬編碼了 `127.0.0.1` 作為資料庫主機
3. **CloudSQL Proxy 連接方式錯誤**：在 Kubernetes 環境中，CloudSQL Proxy 通過 Unix socket 提供連接，而不是 TCP 連接

## 解決方案

修改 `configmaps/src/repos/qtm-api/database.php` 設定檔案：

**修改前：**
```php
'write' => [
    'host' => '127.0.0.1',
    'port' => '3306'
],
'read' => [
    'host' => '127.0.0.1',
    'port' => '3307',
    // ...
],
```

**修改後：**
```php
'write' => [
    'unix_socket' => '/cloudsql/long-disk-213608:us-central1:sta-db-003',
],
'read' => [
    'unix_socket' => '/cloudsql/long-disk-213608:us-central1:sta-db-003-replica',
    // ...
],
```

## 技術細節

- CloudSQL Proxy 在 `/cloudsql/` 目錄下創建 Unix socket 檔案
- 主資料庫 socket：`/cloudsql/long-disk-213608:us-central1:sta-db-003`
- 讀取副本 socket：`/cloudsql/long-disk-213608:us-central1:sta-db-003-replica`
- 使用 Unix socket 連接比 TCP 連接更穩定且效能更好

## 預期結果

修改後，`php artisan queue:work --queue=batching` 程序應該能夠：
1. 正常啟動並保持運行
2. 成功連接到 CloudSQL 資料庫
3. 正確處理隊列中的任務 