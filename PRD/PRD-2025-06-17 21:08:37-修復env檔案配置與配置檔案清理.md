# PRD-2025-06-17 21:08:37-修復env檔案配置與配置檔案清理

## 問題描述

### 主要問題
1. **Laravel .env 檔案缺失問題**：部署後的 pod 中 Laravel 應用程式目錄沒有 `.env` 檔案
2. **SSL 憑證清理問題**：手動創建的不必要 SSL 憑證需要刪除
3. **配置檔案冗余分析**：configmaps/src 目錄中可能存在重複或不需要的檔案

### 技術背景
- Laravel 框架需要在應用程式根目錄找到 `.env` 檔案
- ConfigMap 中的 `.env` 檔案掛載在 `/configmaps/.env`，但初始化腳本期望 `env.${REPO_ENV}` 格式
- Google Cloud Load Balancer 更新需要較長時間才能生效

## 解決方案

### 1. ✅ 修復 .env 檔案配置

**問題分析：**
- `update_configmaps.sh` 正確將 `env.${REPO_ENV}` 重命名為 `.env` 
- 但 `init_php.sh` 和 `init_cron.sh` 仍期望 `env.${REPO_ENV}` 格式

**修復步驟：**
1. 修改 `configmaps/src/all/scripts/init_php.sh`：
   ```bash
   # 原來：cp -f ${__DIR__}/env.${REPO_ENV}  ${repo}/.env
   # 修改為：cp -f ${__DIR__}/.env  ${repo}/.env
   ```

2. 修改 `configmaps/src/all/scripts/init_cron.sh`：
   ```bash
   # 原來：cp -f ${__DIR__}/env.${REPO_ENV}  ${repo}/.env
   # 修改為：cp -f ${__DIR__}/.env  ${repo}/.env
   ```

3. 重新創建 ConfigMap 包含所有必要檔案：
   ```bash
   kubectl delete configmap configmaps
   kubectl create configmap configmaps \
     --from-file=configmaps/src/all/configs/ \
     --from-file=configmaps/src/all/scripts/ \
     --from-file=configmaps/src/all/secrets/ \
     --from-file=configmaps/src/all/test_scripts/ \
     --from-file=configmaps/src/repos/qtm-api/ \
     --from-file=.env=/tmp/.env
   ```

4. 重啟相關 deployment：
   ```bash
   kubectl rollout restart deployment/app-v20250617201045
   kubectl rollout restart deployment/cron-v20250617201045
   ```

**驗證結果：**
- ✅ Pod 中 Laravel 應用程式目錄存在 `.env` 檔案
- ✅ API 健康檢查成功：`{"status":"ok","database":"connected"}`

### 2. ⏳ SSL 憑證清理（進行中）

**問題：**
手動創建的憑證 `mcrt-469bf071-5bc2-4b01-b8ca-07fcfff8cb83` 仍被 target proxy 使用

**已執行的修復：**
1. 更新 ingress 配置移除對新憑證的依賴
2. 直接更新 target-https-proxy 配置
3. 等待 Google Cloud Load Balancer 更新生效

**待完成：**
- 繼續等待 Google Cloud 更新（可能需要 10-30 分鐘）
- 稍後執行：`gcloud compute ssl-certificates delete mcrt-469bf071-5bc2-4b01-b8ca-07fcfff8cb83`

### 3. ✅ 配置檔案清理

**分析結果：**
- **保留所有檔案**：經分析，configmaps/src 中的檔案都有各自用途
- **清理暫存目錄**：刪除 `configmaps/dist/` 目錄並添加到 `.gitignore`

**配置檔案用途分析：**
- `configmaps/src/all/` - 當前使用的靜態配置
- `configmaps/templates/` - 新配置管理系統的模板（備用系統）
- `configmaps/scripts/` - 配置生成和驗證工具
- `configmaps/environments/` - 基礎設施配置（與應用程式配置不同）

## 技術改進

### 配置管理架構
系統現在支援兩種配置模式：
1. **靜態模式**（當前使用）：直接使用固定配置檔案
2. **動態模式**（備用）：使用模板和環境變數生成配置

### 檔案清理
- 添加 `configmaps/dist/` 到 `.gitignore`
- 刪除暫存目錄避免版本控制污染

## 測試與驗證

### 功能測試
- ✅ Laravel 應用程式正確讀取 `.env` 檔案
- ✅ 資料庫連接正常
- ✅ API 端點回應正常
- ✅ Cron 和 Horizon 服務啟動正常

### 系統狀態
```bash
# Pod 狀態
kubectl get pods
# ConfigMap 內容
kubectl get configmap configmaps -o json | jq '.data | keys[]'
# API 健康檢查
curl -s https://sta-qtm-api.qtmedical.com/api/healthz
```

## 後續行動

### 立即行動
1. ⏳ 繼續監控 SSL 憑證刪除狀態
2. ✅ 提交代碼變更到版本控制

### 建議改進
1. 考慮啟用動態配置管理系統以支援多環境部署
2. 建立配置檔案變更的自動化測試
3. 優化 ConfigMap 更新流程

## 相關檔案

### 修改的檔案
- `configmaps/src/all/scripts/init_php.sh`
- `configmaps/src/all/scripts/init_cron.sh`
- `.gitignore`

### 刪除的檔案
- `configmaps/dist/` (整個目錄)

### 相關配置
- Kubernetes ConfigMap: `configmaps`
- Deployments: `app-v20250617201045`, `cron-v20250617201045`
- SSL Certificate: `mcrt-469bf071-5bc2-4b01-b8ca-07fcfff8cb83` (待刪除)

---

**執行時間：** 2025-06-17 21:08:37  
**狀態：** 主要問題已解決，SSL 憑證清理進行中  
**影響範圍：** Laravel 應用程式配置、ConfigMap 管理、SSL 憑證管理 