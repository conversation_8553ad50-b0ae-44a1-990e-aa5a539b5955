# PRD-2025-06-17 16:11:47-修復Cron容器env檔案缺失問題

## 問題描述

使用者回報使用 `@cloud-deploy` 部署後，cron pod 上面不存在 `.env` 檔案，但 php pod 上面則是有 `.env` 檔案。

## 問題分析

經過診斷發現問題根源：

### 1. 問題現象
- Cron Pod: `/var/www/html/repo/.env` 檔案不存在
- PHP Pod: `/var/www/html/repo/.env` 檔案存在且正常

### 2. 根本原因
1. **ConfigMap 中缺少 `env.sta` 檔案**：雖然 ConfigMap 包含了 `.env` 檔案，但沒有 `env.${REPO_ENV}` 檔案
2. **初始化腳本依賴環境特定檔案**：
   - `init_cron.sh` 和 `init_php.sh` 都執行：`cp -f ${__DIR__}/env.${REPO_ENV} ${repo}/.env`
   - 當 `REPO_ENV=sta` 時，腳本尋找 `/configmaps/env.sta` 檔案
   - 由於 ConfigMap 中沒有 `env.sta` 檔案，複製失敗，導致 cron pod 沒有 `.env` 檔案

### 3. 為什麼 PHP Pod 有 .env 檔案
初步分析顯示可能存在其他機制或之前的部署遺留檔案。

## 解決方案

### 修改 `tools/update_configmaps.sh`

在第 144-147 行新增環境特定檔案的複製：

```bash
# 複製環境檔案：同時提供 .env 和 env.${REPO_ENV} 兩個檔案
cp -f /workspace/configmaps/src/repos/${_REPO_NAME}/env.${_REPO_ENV} /workspace/configmaps/dist/.env 2>/dev/null || echo "Warning: No env file found"
cp -f /workspace/configmaps/src/repos/${_REPO_NAME}/env.${_REPO_ENV} /workspace/configmaps/dist/env.${_REPO_ENV} 2>/dev/null || echo "Warning: No env.${_REPO_ENV} file found"
```

### 修改說明
1. **保持向後相容性**：繼續提供 `.env` 檔案
2. **支援初始化腳本**：新增 `env.${REPO_ENV}` 檔案到 ConfigMap
3. **統一 cron 和 php pod 行為**：確保兩個容器使用相同的環境檔案來源

## 影響範圍

### 正面影響
- ✅ 修復 cron pod 缺少 `.env` 檔案的問題
- ✅ 確保 cron 和 php pod 環境設定一致
- ✅ 保持現有架構設計不變

### 潛在風險
- 🔄 需要重新部署以套用修改
- 📝 ConfigMap 會包含重複的環境檔案內容（`.env` 和 `env.sta`）

## 驗證方法

部署後檢查：

```bash
# 檢查 cron pod 的 .env 檔案
kubectl exec <cron-pod> -c php-cron -- ls -la /var/www/html/repo/.env

# 檢查 ConfigMap 中的檔案
kubectl get configmap configmaps -o yaml | grep "env.sta\|\.env"

# 比較兩個容器的環境檔案
kubectl exec <cron-pod> -c php-cron -- cat /var/www/html/repo/.env
kubectl exec <php-pod> -c php-fpm -- cat /var/www/html/repo/.env
```

## 後續改進建議

1. **統一環境檔案命名**：考慮讓所有初始化腳本都使用 `.env` 檔案
2. **增強錯誤處理**：在初始化腳本中增加檔案存在性檢查
3. **自動化測試**：增加部署後的環境檔案驗證步驟

## 相關檔案

- `tools/update_configmaps.sh` - ConfigMap 更新腳本（已修改）
- `configmaps/src/all/scripts/init_cron.sh` - Cron 容器初始化腳本
- `configmaps/src/all/scripts/init_php.sh` - PHP 容器初始化腳本
- `configmaps/src/repos/qtm-api/env.sta` - 測試環境配置檔案

## 修復狀態

- [x] 問題診斷完成
- [x] 解決方案實施
- [ ] 部署驗證
- [ ] 測試確認 