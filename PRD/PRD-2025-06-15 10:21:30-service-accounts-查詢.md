# PRD - Service Accounts 查詢

**建立時間**: 2025-06-15 10:21:30  
**需求類型**: 專案配置查詢  
**狀態**: 已完成

## 需求描述

用戶詢問專案內是否有引用到特定的 service account `<EMAIL>`，以及專案中是否有其他 service accounts 的使用。

## 查詢結果

### 發現的 Service Accounts

專案中共發現 **3 個 Google Cloud Service Accounts**，全部屬於 `long-disk-213608` 專案：

1. **<EMAIL>**
   - 檔案位置: `configmaps/src/all/secrets/cloud-storage.json`
   - 用途: Cloud Storage API 存取
   - 使用位置:
     - `configmaps/src/all/scripts/init_cron.sh` (第19行)
     - `configmaps/src/all/scripts/init_php.sh` (第19行)

2. **<EMAIL>**
   - 檔案位置: `configmaps/src/all/secrets/proxyuser.json`
   - 用途: Cloud SQL Proxy 連線
   - 使用位置:
     - `k8s-yaml/bundle/bundle.template.yaml` (第201行)
     - `k8s-yaml/bundle/deployment.template.yaml` (第193行)
     - `k8s-yaml/bundle/bundle.cron.template.yaml` (第186行)
     - `tools/open__cloudsqlproxy_pro` (第3行)
     - `tools/open__cloudsqlproxy_sta` (第3行)

3. **<EMAIL>**
   - 檔案位置: `configmaps/src/all/secrets/proxyuser-viewer.json`
   - 用途: Cloud SQL Proxy 檢視權限
   - 使用位置:
     - `tools/open__cloudsqlproxy_fra` (第3行)
     - `tools/open__cloudsqlproxy_aus` (第3行)

### Service Account 配置檔案結構

所有 service account 檔案都包含完整的 Google Cloud 服務帳戶金鑰資訊：
- `type`: "service_account"
- `project_id`: "long-disk-213608"
- `private_key_id`: 唯一識別碼
- `private_key`: RSA 私鑰 (PEM 格式)
- `client_email`: Service account 電子郵件地址
- `client_id`: 客戶端 ID
- `auth_uri`, `token_uri`: OAuth 認證端點
- `client_x509_cert_url`: X.509 憑證 URL

### 使用模式分析

1. **Cloud Storage 認證**: 用於 cron 和 PHP 初始化腳本中的 gcloud 認證
2. **Cloud SQL Proxy**: 用於 Kubernetes 部署中的資料庫連線代理
3. **環境區分**: 不同環境使用不同的 service account (pro/sta vs fra/aus)

## 安全性注意事項

- 所有 service account 私鑰都以明文形式儲存在配置檔案中
- 建議考慮使用 Kubernetes Secrets 或其他安全儲存方式
- 定期輪換 service account 金鑰

## 結論

**是的**，專案中確實有引用到用戶詢問的 service account `<EMAIL>`，並且還有另外兩個相關的 service accounts 用於不同的 Google Cloud 服務存取。 