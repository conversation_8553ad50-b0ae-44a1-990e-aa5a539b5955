# 修復 Cloud Build Substitution 錯誤

## 問題背景

在實施零停機藍綠部署解決方案後，用戶在執行部署時遇到了 Cloud Build substitution 錯誤：

```
ERROR: (gcloud.builds.submit) INVALID_ARGUMENT: generic::invalid_argument: invalid value for 'build.substitutions': key in the template "DEPLOY_STRATEGY" is not a valid built-in substitution
```

## 問題分析

### 根本原因

1. **Substitution 變數命名衝突**
   - 使用了 `_DEPLOY_STRATEGY` 作為 substitution 變數名稱
   - Cloud Build 將其誤認為內建的 substitution 變數
   - 導致驗證失敗

2. **環境變數引用複雜性**
   - 在環境變數中使用了複雜的變數引用
   - Cloud Build 在解析模板時檢查所有變數引用
   - 非內建變數名稱導致驗證錯誤

### 錯誤的配置

```yaml
substitutions:
  _DEPLOY_STRATEGY: 'v3'  # 問題：這個名稱被誤認為內建變數

env:
  - 'DEPLOY_STRATEGY=${_DEPLOY_STRATEGY}'  # 問題：複雜的環境變數引用
```

## 解決方案

### 修正 1：簡化 Substitution 變數名稱

將 `_DEPLOY_STRATEGY` 改為 `_STRATEGY`，避免使用可能與內建變數衝突的名稱：

```yaml
substitutions:
  _STRATEGY: 'v3'  # 修正：使用簡短且不會衝突的名稱
```

### 修正 2：簡化環境變數引用

移除環境變數中的複雜引用，直接在腳本中使用 substitution：

```yaml
# 修正前
env:
  - 'DEPLOY_STRATEGY=${_DEPLOY_STRATEGY}'
args:
  - '-c'
  - |
    DEPLOY_STRATEGY="${DEPLOY_STRATEGY:-v3}"

# 修正後
env:
  - 'VERSION_NEXT=${_VERSION_NEXT}'
args:
  - '-c'
  - |
    STRATEGY="${_STRATEGY}"  # 直接使用 substitution
```

### 修正 3：更新 cloud-deploy 腳本

更新 cloud-deploy 工具中的 substitution 參數：

```bash
# 修正前
substitutions="$substitutions,_DEPLOY_STRATEGY=$DEPLOY_STRATEGY"

# 修正後  
substitutions="$substitutions,_STRATEGY=$DEPLOY_STRATEGY"
```

## 實施細節

### 完整的修正配置

```yaml
# 11. 等待部署就緒並切換服務（零停機）
- name: 'gcr.io/cloud-builders/gcloud'
  id: 'Wait for Deployment and Switch Service'
  entrypoint: 'bash'
  env:
    - 'VERSION_NEXT=${_VERSION_NEXT}'
  args:
    - '-c'
    - |
      # 獲取 kubeconfig
      gcloud container clusters get-credentials ${_CLUSTER_NAME} --zone=${_COMPUTE_ZONE} --project=${_PROJECT_ID}
      
      # 選擇部署策略版本
      STRATEGY="${_STRATEGY}"
      
      if [ "$STRATEGY" = "v3" ]; then
        echo "使用 V3 部署策略（深度健康檢查）"
        chmod +x /workspace/tools/k8s_wait_and_switch_v3.sh
        /workspace/tools/k8s_wait_and_switch_v3.sh
      else
        echo "使用 V2 部署策略（基本零停機）"
        chmod +x /workspace/tools/k8s_wait_and_switch_v2.sh
        /workspace/tools/k8s_wait_and_switch_v2.sh
      fi

substitutions:
  _REPO_NAME: ''        # Git 倉庫名稱
  _REPO_ENV: ''         # 部署環境 (dev, sta, pro)
  _REPO_REF: ''         # Git 分支或 commit hash
  _VERSION_NEXT: ''     # 新版本號
  _PROJECT_ID: ''       # GCP 專案 ID
  _CLUSTER_NAME: ''     # GKE 叢集名稱
  _COMPUTE_ZONE: ''     # GKE 叢集所在區域
  _GIT_URL: ''          # Git 倉庫 URL
  _STRATEGY: 'v3'       # 部署策略版本 (v2 或 v3)
```

### 測試過程

1. **創建簡化測試配置**
   - 使用最小化的 Cloud Build 配置測試 substitution
   - 確認變數名稱不會與內建變數衝突

2. **逐步修正配置**
   - 先修正 substitution 變數名稱
   - 再簡化環境變數引用
   - 最後測試完整的部署流程

3. **驗證修正結果**
   - 確認 Cloud Build 可以正常啟動
   - 驗證部署策略選擇機制正常工作

## 最終使用方式

### 命令行選項

```bash
# 使用 V3 策略（預設，推薦）
./tools/cloud-deploy qtm-api sta develop

# 使用 V2 策略（較快但較不安全）
./tools/cloud-deploy qtm-api sta develop --deploy-strategy v2

# 使用 V3 策略（明確指定）
./tools/cloud-deploy qtm-api sta develop --deploy-strategy v3
```

### 直接 Cloud Build 命令

```bash
gcloud builds submit --config=cloudbuild.yaml \
  --substitutions=_REPO_NAME=qtm-api,_REPO_ENV=sta,_REPO_REF=develop,_VERSION_NEXT=v20250617225800,_PROJECT_ID=long-disk-213608,_CLUSTER_NAME=sta-qtm-api,_COMPUTE_ZONE=us-central1-c,_GIT_URL=https://github.com/qtmedical/qtm-api.git,_STRATEGY=v3 .
```

## 部署策略說明

### V2 策略（基本零停機）
- 改進的健康檢查
- Endpoints 驗證
- 基本的雙版本共存
- 執行時間較短

### V3 策略（推薦）
- 深度健康檢查（等待所有容器的 readiness probe）
- Session Affinity 防止連接中斷
- 漸進式縮減舊版本
- 自動回滾機制
- 服務穩定性監控
- 執行時間較長但更安全

## 預期效果

1. **解決 502 錯誤**
   - V3 策略通過深度健康檢查確保新版本完全就緒
   - Session Affinity 保護現有連接不中斷
   - 漸進式切換避免突然的流量遷移

2. **提供靈活性**
   - 用戶可以根據需要選擇不同的部署策略
   - 支援快速部署（V2）和安全部署（V3）

3. **改善用戶體驗**
   - 修復了 Cloud Build 錯誤，部署流程更穩定
   - 提供清晰的策略選擇和狀態反饋

## 學習總結

### Cloud Build Substitution 最佳實踐

1. **變數命名**
   - 避免使用可能與內建變數衝突的名稱
   - 使用簡短且描述性的名稱
   - 所有自定義變數必須以底線 `_` 開頭

2. **環境變數使用**
   - 盡量簡化環境變數引用
   - 避免在環境變數中使用複雜的變數替換
   - 直接在腳本中使用 substitution 變數

3. **測試策略**
   - 使用簡化的配置測試新的 substitution 變數
   - 逐步增加複雜性
   - 在生產環境使用前進行充分測試

### 部署策略設計原則

1. **漸進式改進**
   - 從基本的零停機部署開始
   - 逐步增加安全性和穩定性檢查
   - 提供多種策略選擇

2. **向後相容性**
   - 保持現有部署流程的相容性
   - 提供預設值確保平滑過渡
   - 允許用戶選擇適合的策略

3. **監控和驗證**
   - 在每個階段進行充分的健康檢查
   - 提供詳細的狀態反饋
   - 實施自動回滾機制 