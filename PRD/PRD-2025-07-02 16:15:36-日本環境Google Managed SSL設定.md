# PRD - 日本環境 Google Managed SSL 設定

**建立日期**: 2025-07-02 16:15:36  
**狀態**: 已完成  
**優先級**: 高  

## 需求背景

用戶需要為日本環境 (jpn) 設定 Google Managed SSL 憑證，以提供 HTTPS 加密連接給以下域名：
- `jp-qtm-api.qtmedical.com`
- `jp-dashboard.qtmedical.com`

## 需求描述

### 主要需求
1. **SSL 憑證配置**: 使用 Google Managed SSL 而非 Let's Encrypt
2. **域名保護**: 為兩個日本環境域名提供 SSL 憑證
3. **自動化管理**: 利用 Google Cloud 自動化憑證生命週期管理
4. **Ingress 整合**: 將 SSL 憑證整合到 Kubernetes Ingress 配置中

### 技術要求
- 使用 GKE ManagedCertificate CRD
- 配置 Ingress 引用憑證
- 確保與現有 LoadBalancer 和靜態 IP 整合
- 提供完整的部署和驗證步驟

## 解決方案

### 1. 配置文件創建

#### SSL 憑證配置文件
**檔案**: `k8s-yaml/ssl-google-managed/jpn-ssl-google-managed.yaml`

```yaml
# 建立 Google-managed SSL certificates
# 注意：同一個 subdomain 同時間只能掛一個 Google-managed SSL certificates

apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: jpn-google-managed-certificate
spec:
  domains:
    - jp-dashboard.qtmedical.com
    - jp-qtm-api.qtmedical.com
```

#### Ingress 配置文件
**檔案**: `k8s-yaml/ingress-loadbalancer-with-ssl/jpn-ingress-80-443.yaml`

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jpn-prod-ingress-with-tls-https
  annotations:
    kubernetes.io/ingress.global-static-ip-name: jpn-prod-static-ip
    networking.gke.io/managed-certificates: jpn-google-managed-certificate
    kubernetes.io/ingress.class: "gce"
spec:
  defaultBackend:
    service:
      name: app
      port: 
        number: 80
```

### 2. 部署步驟

#### 前置條件檢查
- ✅ DNS A records 已設定指向 LoadBalancer EXTERNAL-IP (************)
- ✅ LoadBalancer 服務正常運行
- ✅ 基礎 Kubernetes 服務已部署

#### 部署順序
1. **部署 SSL 憑證配置**
   ```bash
   kubectl apply -f k8s-yaml/ssl-google-managed/jpn-ssl-google-managed.yaml
   ```

2. **驗證憑證狀態**
   ```bash
   kubectl get managedcertificate jpn-google-managed-certificate
   kubectl describe managedcertificate jpn-google-managed-certificate
   ```

3. **部署 Ingress 配置**
   ```bash
   kubectl apply -f k8s-yaml/ingress-loadbalancer-with-ssl/jpn-ingress-80-443.yaml
   ```

4. **驗證部署**
   ```bash
   kubectl get ingress jpn-prod-ingress-with-tls-https
   curl -I https://jp-qtm-api.qtmedical.com
   curl -I https://jp-dashboard.qtmedical.com
   ```

### 3. 文件更新

#### 部署指南更新
更新 `docs/japan-deployment-guide.md` 文件，包含：

1. **詳細 SSL 設定步驟**:
   - 分步驟說明憑證和 Ingress 部署
   - 驗證命令和狀態檢查
   - 重要注意事項和時間預期

2. **故障排除章節**:
   - SSL 憑證常見問題
   - DNS 解析檢查方法
   - 憑證 provisioning 狀態檢查

3. **配置文件列表更新**:
   - 新增 SSL 憑證配置文件
   - 新增 Ingress 配置文件

## 技術說明

### Google Managed SSL 特性
1. **自動化管理**: Google 自動處理憑證更新和生命週期
2. **免費服務**: 不收取額外費用
3. **高可用性**: 與 Google Load Balancer 深度整合
4. **Domain Validation**: 通過 DNS 驗證域名所有權

### 重要限制和注意事項
1. **憑證 Provisioning 時間**: 10-60 分鐘
2. **DNS 依賴**: 需要 DNS 解析正常才能完成 provisioning
3. **域名限制**: 同一個 subdomain 同時只能有一個 Google-managed SSL certificate
4. **區域限制**: 僅適用於 Google Cloud Load Balancer

### 與其他環境的一致性
- 配置模式與現有環境 (eu, au, fra) 保持一致
- 命名規範遵循 `{env}-google-managed-certificate` 模式
- Ingress 配置參考法國環境的成功模式

## 驗證標準

### 成功標準
1. **憑證狀態**: ManagedCertificate 狀態為 `Active`
2. **HTTPS 存取**: 兩個域名都能通過 HTTPS 正常訪問
3. **憑證有效性**: 瀏覽器顯示有效的 SSL 憑證
4. **自動重導向**: HTTP 流量自動重導向到 HTTPS (如已配置)

### 驗證命令
```bash
# 檢查憑證狀態
kubectl get managedcertificate jpn-google-managed-certificate

# 檢查 Ingress 狀態
kubectl get ingress jpn-prod-ingress-with-tls-https

# 測試 HTTPS 連接
curl -I https://jp-qtm-api.qtmedical.com
curl -I https://jp-dashboard.qtmedical.com

# 檢查 DNS 解析
nslookup jp-qtm-api.qtmedical.com
nslookup jp-dashboard.qtmedical.com
```

## 後續行動

### 立即行動
- [ ] 執行憑證配置部署
- [ ] 監控憑證 provisioning 進度
- [ ] 驗證 HTTPS 存取

### 長期維護
- [ ] 設定憑證狀態監控
- [ ] 建立憑證更新流程 (雖然是自動的，但需要監控)
- [ ] 整合到 CI/CD 部署流程

## 相關檔案

### 新建檔案
- `k8s-yaml/ssl-google-managed/jpn-ssl-google-managed.yaml`
- `k8s-yaml/ingress-loadbalancer-with-ssl/jpn-ingress-80-443.yaml`

### 更新檔案
- `docs/japan-deployment-guide.md`

## 風險評估

### 潛在風險
1. **DNS 解析延遲**: DNS 傳播可能影響憑證 provisioning
2. **憑證 provisioning 失敗**: 域名驗證問題可能導致失敗
3. **流量中斷**: Ingress 更新期間可能短暫影響服務

### 緩解措施
1. **分段部署**: 先部署憑證，再部署 Ingress
2. **狀態監控**: 密切監控憑證 provisioning 狀態
3. **回滾計劃**: 保留現有 LoadBalancer 配置作為後備

## 學習要點

1. **Google Managed SSL 工作流程**: 理解憑證自動化管理機制
2. **Kubernetes Ingress 與 SSL 整合**: 掌握 annotations 使用方法
3. **DNS 與 SSL 依賴關係**: 理解 domain validation 要求
4. **多域名憑證管理**: 單一憑證管理多個子域名

---

**負責人**: DevOps 團隊  
**審核人**: 系統管理員  
**最後更新**: 2025-07-02 16:15:36 