# 部署回滾機制優化與自動化

## 日期

2025-06-18 13:56:18

## 需求背景

使用者提出，目前的部署流程雖然是零停機，但在部署後會直接刪除舊版本的資源，導致無法快速回滾。希望能增加一個保留舊版並能快速回滾的機制。

## 執行過程與解決方案

### 1. 修改部署腳本以保留舊版本

- **分析**: `tools/k8s_wait_and_switch_v3.sh` 腳本的 `Phase 5` 會使用 `kubectl delete` 清理舊的 Deployment 和 HPA。
- **修改**: 將 `Phase 5` 中的 `kubectl delete deploy` 和 `kubectl delete hpa` 命令註解掉，並加入提示訊息，告知使用者舊版本將被保留以供回滾。舊的 CronJob 則按原樣刪除，因為其回滾需求較低。
- **結果**: 部署後，舊版本的 Deployment 和 HPA 會被縮容至 0 個副本並保留，而不是被刪除。

### 2. 建立自動化回滾腳本

為了避免手動回滾的複雜性和潛在失誤，建立了一個新的自動化腳本 `tools/rollback-deployment`。

- **功能**:
    - 自動偵測指定環境（sta, pro, aus, fra）。
    - 找出當前活躍的 Deployment 和所有可回滾的舊版本（副本數為 0）。
    - 提供互動式選單，讓使用者選擇要回滾的目標版本。
    - 執行前顯示詳細的回滾計畫，並要求使用者最終確認。

### 3. 優化回滾腳本以實現零停機

- **問題**: 初版的回滾腳本在切換流量時，會產生短暫的 502 錯誤 (Downtime)。
- **分析**: 這是因為 `kubectl patch service` 的變更需要時間傳播到所有節點的 `kube-proxy`。
- **解決方案**: 升級回滾腳本，採用類似藍綠部署的策略：
    1.  **擴容舊版**並等待其完全就緒。
    2.  **流量共存**：先將 Service 指向一個通用標籤，讓回滾目標版本和當前版本同時接收流量。
    3.  **深度健康檢查**：主動探測回滾目標 Pod 的 `/api/healthz` 端點，確保其真正可用。
    4.  **安全縮容**：在確認回滾版本健康後，才將有問題的當前版本縮容到 0。
    5.  **最終切換**：最後才將 Service 的流量完全指向回滾成功的版本。

### 4. 腳本相容性修復

- **問題**: 回滾腳本在 macOS 環境下執行時出現 `mapfile: command not found` 錯誤。
- **分析**: `mapfile` 是 bash 4.0+ 的功能，而 macOS 預設的 bash 版本較舊。
- **解決方案**: 將 `mapfile` 指令替換為相容性更好的 `while read` 迴圈，確保腳本的通用性。

### 5. 清理舊腳本

- 根據使用者要求，刪除了不再使用的舊版部署腳本 `tools/k8s_wait_and_switch.sh` 和 `tools/k8s_wait_and_switch_v2.sh`，保持專案整潔。

## 經驗總結 (Lessons Learned)

1.  **回滾機制是發布流程的必要一環**：僅有零停機部署是不夠的，必須能夠在出現問題時快速、安全地回到上一個穩定狀態。
2.  **回滾應視為一次反向的"部署"**：簡單的流量切換是不夠的，回滾過程也需要完整的健康檢查和流量共存階段，才能避免二次故障。
3.  **自動化是可靠性的關鍵**：將複雜的回滾步驟封裝成自動化腳本，可以大幅減少人為失誤，並縮短故障恢復時間 (MTTR)。
4.  **腳本的相容性很重要**：在開發工具腳本時，應考慮到不同作業系統和環境的差異（如 Bash 版本），盡量使用通用的指令以避免執行時錯誤。
5.  **保留但不堆積**：保留舊版本是為了應急，但也需要建立一個配套的清理機制，例如定期手動或通過 CronJob 清理超過一定時間（如 72 小時）且未被使用的舊 Deployment，以避免資源浪費。 