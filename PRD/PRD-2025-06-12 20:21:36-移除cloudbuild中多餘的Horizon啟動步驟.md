# Laravel Horizon 部署流程優化 - 移除多餘啟動步驟

## 專案資訊
- **日期**: 2025-06-12 20:21:36
- **類型**: 部署流程優化
- **影響範圍**: Cloud Build 部署流程
- **狀態**: 已完成

## 問題描述

### 背景
在先前的 Laravel Horizon 啟動問題修復過程中，我們發現了 Cloud Build 部署流程中存在重複且有邏輯錯誤的 Horizon 啟動步驟。

### 發現的問題
1. **重複啟動邏輯**: 
   - `configmaps/src/all/scripts/init_cron.sh` 已經包含完整的 `start_horizon()` 函數
   - `cloudbuild.yaml` 中也有 "Start Laravel Horizon Queue Server" 步驟
   
2. **邏輯錯誤**:
   - cloudbuild.yaml 中的步驟嘗試在**所有 Pod**中啟動 Horizon
   - 但根據系統架構，Horizon 應該**只在 cron 容器中**運行
   - 使用了錯誤的容器名稱 (`php-fpm` 而非 `php-cron`)

3. **架構混亂**:
   - 造成部署流程複雜化
   - 可能導致 Horizon 在不應該運行的容器中啟動
   - 增加故障排除的困難度

## 解決方案

### 選擇的方案
**移除 cloudbuild.yaml 中的 Horizon 啟動步驟**

### 理由
1. **單一責任原則**: `init_cron.sh` 已經負責在 cron 容器啟動時處理 Horizon
2. **避免重複**: 消除重複的啟動邏輯
3. **正確的容器目標**: init_cron.sh 在正確的容器中執行
4. **簡化部署流程**: 減少不必要的複雜性

## 技術實現

### 修改的檔案
- `cloudbuild.yaml`: 移除整個 "Start Laravel Horizon Queue Server" 步驟

### 修改前 (cloudbuild.yaml)
```yaml
# 12. 啟動 Laravel Horizon Queue Server
- name: 'asia-east1-docker.pkg.dev/${_PROJECT_ID}/k8s-deploy/k8s-deploy-tools:latest'
  id: 'Start Laravel Horizon Queue Server'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      # ... 複雜的 Pod 等待和 Horizon 啟動邏輯 ...
```

### 修改後 (cloudbuild.yaml)
```yaml
# 12. 部署完成 (Laravel Horizon 會透過 cron 容器的 init_cron.sh 自動啟動)
```

### 保留的機制 (init_cron.sh)
```bash
start_horizon() {
    echo "啟動 Laravel Horizon Queue Server..."
    cd ${repo}
    
    # 檢查是否已有 Horizon 在運行
    if ps aux | grep '[h]orizon' > /dev/null; then
        echo "Horizon 已在運行中，先停止現有程序..."
        pkill -f horizon || true
        sleep 2
    fi
    
    # 啟動 Horizon (在背景執行)
    echo "正在啟動 Horizon..."
    nohup php artisan horizon >/var/log/horizon.log 2>&1 &
    
    # 驗證啟動狀態...
}
```

## 影響評估

### 正面影響
1. **簡化部署流程**: 移除 140+ 行的複雜啟動邏輯
2. **提高可靠性**: 避免在錯誤的容器中啟動 Horizon
3. **減少部署時間**: 移除不必要的等待和檢查步驟
4. **改善維護性**: 單一位置管理 Horizon 啟動邏輯

### 風險評估
- **低風險**: `init_cron.sh` 已經過測試且正常運作
- **回滾策略**: 如需要可恢復 cloudbuild.yaml 中的步驟

## 現有的啟動機制

### init_cron.sh 中的 Horizon 啟動流程
1. **檢查現有程序**: 確認是否已有 Horizon 在運行
2. **安全停止**: 停止現有程序並等待
3. **啟動新實例**: 使用 nohup 在背景啟動
4. **狀態驗證**: 確認啟動成功
5. **日誌管理**: 輸出到專用日誌檔案

### 容器執行環境
- **正確容器**: cron 容器 (label: `version=cron-$VERSION`)
- **正確時機**: 容器啟動時自動執行
- **正確權限**: 在應用程式環境中執行

## 測試計劃

### 驗證步驟
1. **部署測試**: 執行完整的部署流程
2. **Horizon 狀態檢查**: 確認 Horizon 正常啟動
3. **日誌檢查**: 驗證啟動日誌正常
4. **功能測試**: 確認 queue 處理正常

### 緊急修復工具
如果發現問題，可使用現有的緊急修復工具：
```bash
./tools/start-horizon.sh sta
```

## 總結

這次優化移除了 Cloud Build 中多餘且有問題的 Horizon 啟動步驟，讓 Horizon 啟動完全由 cron 容器的 `init_cron.sh` 負責處理。這符合單一責任原則，簡化了部署流程，並確保 Horizon 在正確的容器中啟動。

### 關鍵改進
- ✅ 移除重複的啟動邏輯
- ✅ 修正容器目標錯誤
- ✅ 簡化部署流程
- ✅ 提高系統可靠性
- ✅ 改善維護性

### 下一步
1. 測試修改後的部署流程
2. 監控 Horizon 啟動狀況
3. 如有需要，進一步優化 init_cron.sh 中的啟動邏輯 