# 需求：修復 php-cron pod 未執行 artisan horizon 的問題

## 問題描述

使用者回報在部署後，`php-cron` Pod 中的 `artisan horizon` 程序沒有執行，導致 Laravel 的隊列任務 (queue jobs) 無法被處理。

**有問題的 Pod 狀況**
- 啟動腳本: `init_cron.sh`
- `ps -aux` 輸出顯示只有 `cron` 和 `tail` 在運行，沒有 `php artisan horizon`。

**正常的 Pod 狀況**
- `ps -aux` 輸出顯示 `php artisan horizon` 以及相關的 worker 程序都在正常運行。

## 分析

經過檢查，問題的根源在於 `configmaps/src/all/scripts/init_cron.sh` 這個啟動腳本。該腳本的設計是為了運行排程任務 (cron jobs)，但其中缺少了啟動 `horizon` 這個常駐服務的指令。

## 解決方案

修改 `configmaps/src/all/scripts/init_cron.sh` 腳本，在其中加入啟動 `horizon` 的指令。

具體修改如下：
1.  在腳本中加入 `cd ${repo}` 以確保後續指令在正確的應用程式目錄下執行。
2.  在背景中啟動 `horizon` 服務：`php artisan horizon &`。

這樣修改後，`php-cron` pod 在啟動時不僅會設定 cron，還會一併啟動 `horizon` 來處理隊列任務，解決了工作的問題。 