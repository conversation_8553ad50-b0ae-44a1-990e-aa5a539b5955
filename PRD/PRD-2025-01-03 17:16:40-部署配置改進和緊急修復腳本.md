# PRD - 部署配置改進和緊急修復腳本

**時間：** 2025-01-03 17:16:40  
**需求類型：** 系統改進與自動化  
**狀態：** 已完成  

## 需求背景

基於 [網站 500 錯誤修復](PRD-2025-01-03 17:02:30-修復網站500錯誤和Ingress路由問題.md) 的經驗，發現部署系統存在以下問題：

1. **標籤不一致問題**：Pod 標籤和服務選擇器不匹配
2. **缺乏緊急修復工具**：需要手動執行多個診斷步驟
3. **配置檔案分散**：缺乏標準化的服務配置

這些問題導致部署失敗時需要大量手動介入，增加了故障恢復時間。

## 需求分析

### 根本原因分析

#### 1. 標籤配置不一致
```yaml
# 部署模板只有 env 和 version 標籤
spec:
  selector:
    matchLabels:
      version: {{VERSION}}
      env: {{REPO_ENV}}

# 但服務選擇器要求 app 標籤
spec:
  selector:
    app: qtm-api  # ❌ Pod 沒有這個標籤
    env: fra
    version: v20250620163756
```

#### 2. 故障診斷複雜性
- 需要檢查 Pod 狀態、服務端點、Ingress 後端等多個環節
- 沒有自動化診斷工具
- 修復步驟需要手動執行，容易出錯

#### 3. 服務配置管理不當
- 缺乏標準化的服務模板
- MySQL 服務需要手動創建
- 不同環境的配置不一致

## 解決方案

### 1. 標籤標準化

#### 1.1 部署模板改進
**檔案：** `k8s-yaml/bundle/bundle.template.yaml`

**修改內容：**
```yaml
# 新增 app 標籤到所有層級
metadata:
  labels:
    app: qtm-api          # ✅ 新增
    name: {{REPO_NAME}}
    version: {{VERSION}}
    env: {{REPO_ENV}}

spec:
  selector:
    matchLabels:
      app: qtm-api          # ✅ 新增
      version: {{VERSION}}
      env: {{REPO_ENV}}

  template:
    metadata:
      labels:
        app: qtm-api        # ✅ 新增
        version: {{VERSION}}
        env: {{REPO_ENV}}
```

#### 1.2 Cron 部署模板改進
**檔案：** `k8s-yaml/bundle/bundle.cron.template.yaml`

**修改內容：** 同樣新增 `app: qtm-api` 標籤到所有層級

### 2. 服務配置標準化

#### 2.1 標準化服務模板
**檔案：** `k8s-yaml/service.yaml`

**新增內容：**
```yaml
# App 主服務
apiVersion: v1
kind: Service
metadata:
  name: app
  labels:
    app: qtm-api
    service-type: main
spec:
  type: NodePort
  selector:
    app: qtm-api
    env: fra  # 部署時會被替換
  ports:
  - name: http
    port: 80
    targetPort: 80
  - name: https
    port: 443
    targetPort: 443

---
# MySQL 資料庫代理服務
apiVersion: v1
kind: Service
metadata:
  name: mysql
  labels:
    app: qtm-api
    service-type: database
spec:
  type: ClusterIP
  selector:
    app: qtm-api
    env: fra  # 部署時會被替換
  ports:
  - name: mysql
    port: 3306
    targetPort: 3306
```

#### 2.2 NodePort 服務更新
**檔案：** `k8s-yaml/bundle/nodeport-80-443.yaml`

**修改內容：** 新增 `app: qtm-api` 到選擇器

### 3. 緊急修復腳本

#### 3.1 腳本功能
**檔案：** `tools/emergency-fix-deployment.sh`

**主要功能：**
1. **自動診斷**：檢查 Pod、服務、Ingress 狀態
2. **智能修復**：根據 Pod 標籤自動調整服務選擇器
3. **健康檢查**：測試資料庫連線和應用程式健康狀態
4. **詳細報告**：提供修復摘要和後續建議

#### 3.2 使用方式
```bash
# Fra 環境修復
./tools/emergency-fix-deployment.sh fra

# Dev 環境修復
./tools/emergency-fix-deployment.sh dev

# 預設環境（fra）
./tools/emergency-fix-deployment.sh
```

#### 3.3 修復流程
```
1. 檢查必要工具 (kubectl, jq)
2. 檢查 Pod 狀態
3. 檢查服務端點
4. 如果端點為空：
   - 獲取當前運行版本
   - 檢查 Pod 標籤
   - 根據標籤調整服務選擇器
   - 創建/修復 mysql 服務
5. 檢查 Ingress 狀態
6. 測試資料庫連線
7. 測試應用程式健康狀態
8. 提供修復摘要和建議
```

## 技術規格

### 標籤標準
```yaml
# 所有 Pod 必須包含的標籤
labels:
  app: qtm-api           # 應用程式名稱
  env: {environment}     # 環境名稱
  version: {version}     # 版本號
```

### 服務選擇器標準
```yaml
# 優先使用完整選擇器
selector:
  app: qtm-api
  env: {environment}
  version: {version}

# 如果 Pod 沒有 app 標籤，降級使用
selector:
  env: {environment}
  version: {version}
```

### 診斷檢查清單
1. ✅ Pod 狀態 (Running/Ready)
2. ✅ 服務端點 (app, mysql)
3. ✅ 標籤匹配 (Pod vs Service)
4. ✅ Ingress 後端健康狀態
5. ✅ 資料庫連線 (Cloud SQL Proxy, nginx proxy, service)
6. ✅ 應用程式健康檢查 (/api/version)

## 實作結果

### 修改的檔案

1. **`k8s-yaml/bundle/bundle.template.yaml`**
   - 新增 `app: qtm-api` 標籤到所有層級
   - 統一標籤配置

2. **`k8s-yaml/bundle/bundle.cron.template.yaml`**
   - 新增 `app: qtm-api` 標籤到所有層級

3. **`k8s-yaml/service.yaml`** (新檔案)
   - 標準化的 app 服務配置
   - 標準化的 mysql 服務配置

4. **`k8s-yaml/bundle/nodeport-80-443.yaml`**
   - 更新選擇器包含 `app: qtm-api`

5. **`tools/emergency-fix-deployment.sh`** (新檔案)
   - 緊急修復腳本
   - 自動診斷和修復功能

### 腳本功能特色

#### 智能標籤檢測
```bash
# 檢查 Pod 是否有 app=qtm-api 標籤
if echo "$POD_LABELS" | grep -q "app=qtm-api"; then
    # 使用完整選擇器
    SELECTOR="{\"app\": \"qtm-api\", \"env\": \"${ENV}\", \"version\": \"${CURRENT_VERSION}\"}"
else
    # 降級使用部分選擇器
    SELECTOR="{\"env\": \"${ENV}\", \"version\": \"${CURRENT_VERSION}\"}"
fi
```

#### 自動服務修復
```bash
# 修復 app 服務
kubectl patch svc app -p "{\"spec\":{\"selector\": $SELECTOR}}"

# 自動創建/修復 mysql 服務
if ! kubectl get svc mysql &> /dev/null; then
    # 創建新的 mysql 服務
else
    # 修復現有 mysql 服務
fi
```

#### 全面健康檢查
```bash
# 檢查 Cloud SQL Proxy
kubectl exec $POD_NAME -c nginx -- nc -z 127.0.0.1 33061

# 檢查 nginx mysql proxy
kubectl exec $POD_NAME -c nginx -- nc -z 127.0.0.1 3306

# 檢查 mysql 服務
kubectl exec $POD_NAME -c nginx -- nc -z mysql 3306

# 檢查應用程式
kubectl exec $POD_NAME -c nginx -- curl -s -o /dev/null -w "%{http_code}" http://localhost/api/version
```

## 使用指南

### 緊急修復腳本使用

#### 基本使用
```bash
# Fra 環境（預設）
./tools/emergency-fix-deployment.sh

# 指定環境
./tools/emergency-fix-deployment.sh dev
./tools/emergency-fix-deployment.sh sta
./tools/emergency-fix-deployment.sh aus
./tools/emergency-fix-deployment.sh pro
```

#### 輸出範例
```
=========================================
    QTM Medical 緊急部署修復腳本
=========================================
環境: fra
時間: 2025-01-03 17:16:40
=========================================

步驟 1: 檢查 Pod 狀態
[SUCCESS] 找到 1 個運行中的 Pod

步驟 2: 檢查服務端點
[ERROR] app 服務沒有端點！

步驟 3: 修復服務選擇器
[SUCCESS] app 服務選擇器已修復
[SUCCESS] mysql 服務已創建

...
```

### 部署流程改進

#### 新部署檢查清單
1. ✅ 確認 Pod 包含所有必要標籤
2. ✅ 檢查服務端點是否正確
3. ✅ 驗證 Ingress 後端健康狀態
4. ✅ 測試資料庫連線
5. ✅ 驗證應用程式健康檢查
6. ✅ 如有問題，執行緊急修復腳本

#### 故障排除流程
```
部署失敗 → 執行緊急修復腳本 → 檢查修復結果 → 如問題持續，查看詳細日誌
```

## 預期效益

### 1. 減少故障恢復時間
- **之前**：需要 20-30 分鐘手動診斷和修復
- **現在**：5-10 分鐘自動診斷和修復

### 2. 提高部署成功率
- 標籤一致性避免服務選擇器問題
- 標準化配置減少人為錯誤

### 3. 改善運維體驗
- 一鍵診斷和修復
- 詳細的狀態報告
- 清楚的後續建議

### 4. 降低技術門檻
- 新成員可以快速使用修復腳本
- 減少對專業知識的依賴

## 後續改進建議

### 1. 整合到 CI/CD
- 在部署後自動執行健康檢查
- 失敗時自動觸發修復腳本

### 2. 監控告警
- 設置服務端點數量監控
- Ingress 後端健康狀態告警
- 自動化故障通知

### 3. 配置管理
- 建立配置檔案版本控制
- 標準化所有環境的配置

### 4. 文檔完善
- 更新部署手冊
- 新增故障排除指南
- 培訓材料準備

## 風險評估

### 低風險
- 標籤修改不會影響現有功能
- 腳本只進行診斷和修復，不會破壞現有配置

### 緩解措施
- 腳本有詳細日誌記錄
- 所有操作都有確認和回滾能力
- 修改前會備份原始配置

## 驗證標準

### 功能驗證
1. ✅ 新部署的 Pod 包含所有必要標籤
2. ✅ 服務能正確選擇到 Pod
3. ✅ 緊急修復腳本能正確診斷問題
4. ✅ 腳本能自動修復標籤不匹配問題
5. ✅ 所有健康檢查都能正常執行

### 性能驗證
1. ✅ 修復腳本執行時間 < 5 分鐘
2. ✅ 不影響現有部署性能
3. ✅ 資源使用量無明顯增加

## 總結

這次改進解決了部署系統的核心問題：

1. **標籤標準化**：統一了 Pod 和服務的標籤配置
2. **自動化修復**：提供了一鍵診斷和修復工具
3. **配置標準化**：建立了服務配置的標準模板

通過這些改進，大幅提升了系統的穩定性和可維護性，為未來的擴展打下了良好基礎。

## 相關文件

- [網站 500 錯誤修復](PRD-2025-01-03 17:02:30-修復網站500錯誤和Ingress路由問題.md)
- [資料庫連線修復](PRD-2025-01-03 16:46:00-修復MySQL資料庫連線問題.md)
- [服務版本切換](PRD-2025-01-03 16:25:45-處理服務版本切換問題.md) 