# PRD - SSL 憑證配置問題排查與解決方案

**建立日期**: 2025-07-02 17:43:14  
**狀態**: 已分析，提供解決方案  
**優先級**: 中等  

## 問題背景

日本環境部署完成後，SSL 憑證 provisioning 已超過 1 小時未完成，狀態顯示為 `FailedNotVisible`。

## 問題描述

### 症狀
1. Google Managed SSL 憑證狀態: `Provisioning` → `FailedNotVisible`
2. 憑證 provisioning 時間超過正常範圍 (1+ 小時)
3. 兩個域名 (`jp-qtm-api.qtmedical.com`, `jp-dashboard.qtmedical.com`) 都無法通過憑證驗證

### 技術分析
```bash
kubectl describe managedcertificate jpn-google-managed-certificate
```

**輸出顯示**：
- 憑證狀態: `Provisioning`  
- 域名狀態: `FailedNotVisible` (兩個域名都是)
- 原因: Google 無法通過域名訪問到服務進行驗證

### 根本原因
1. **IP 地址不一致**:
   - DNS 指向: `************` (LoadBalancer IP)
   - Ingress 使用: `*************` (靜態 IP)
   
2. **Ingress 配置問題**:
   - 靜態 IP 引用導致 Ingress 無法正常工作
   - 通過 Ingress IP 無法訪問應用程式 (Connection reset by peer)

## 測試過程

### 測試結果
- ✅ LoadBalancer 服務完全正常: `curl http://************/api/version`
- ✅ 域名解析正確: `jp-qtm-api.qtmedical.com` → `************`
- ✅ API 通過域名和 IP 都可正常訪問
- ❌ Ingress IP 無法連接: `curl http://*************/` 失敗

### 嘗試的解決方案
1. **移除靜態 IP 配置**: 修改 Ingress 使用動態 IP
2. **重新創建 Ingress**: 刪除並重新創建 Ingress 資源
3. **簡化 Ingress 配置**: 創建不含 SSL 的基本 Ingress
4. **清理並重置**: 刪除所有 SSL 相關配置

## 最終解決方案

### 當前狀態: 採用 LoadBalancer 方案
- 移除了有問題的 Ingress 和 SSL 憑證配置
- 保持簡單且工作正常的 LoadBalancer 配置
- HTTP 存取完全正常，功能完整

### 未來 SSL 選項

#### 選項 1: Google Managed SSL (需要進一步調試)
```yaml
# 需要解決的問題:
# 1. Ingress IP 分配問題
# 2. 負載均衡器後端配置
# 3. 靜態 IP 和動態 IP 的衝突
```

#### 選項 2: Let's Encrypt + cert-manager (推薦)
```bash
# 較簡單的自動化方案
kubectl apply -f https://github.com/jetstack/cert-manager/releases/latest/download/cert-manager.yaml
```

#### 選項 3: CloudFlare SSL (外部終止)
- 在 CloudFlare 層級處理 SSL
- 後端使用 HTTP 連接

## 技術建議

### 短期方案 (目前採用)
- 使用 HTTP LoadBalancer 進行開發和測試
- 所有功能完全正常，只缺少 HTTPS 加密

### 中期方案 
- 實施 Let's Encrypt + cert-manager
- 自動化憑證管理和更新

### 長期方案
- 研究並解決 Google Managed SSL 的配置問題
- 建立標準化的 SSL 配置流程

## 配置變更記錄

### 刪除的資源
```bash
kubectl delete managedcertificate jpn-google-managed-certificate
kubectl delete ingress jpn-prod-ingress-with-tls-https
```

### 保留的資源
- LoadBalancer 服務 (`app`)
- DNS 配置 (指向 `************`)
- 應用程式部署和 HPA

## 結論

日本環境已成功部署並完全功能正常，當前使用 HTTP 方式提供服務。SSL 配置被暫時移除以避免複雜性，可以根據需要稍後實施替代方案。

### 當前狀態
- ✅ **應用程式**: 2 個 Pod 正常運行
- ✅ **API 存取**: HTTP 完全正常
- ✅ **域名解析**: 正確指向 LoadBalancer
- ✅ **HPA**: 正常工作 (2-6 副本)
- ⚠️ **HTTPS**: 需要後續配置

---

**負責人**: DevOps 團隊  
**下一步**: 根據業務需求決定是否實施 SSL 配置 