# 重新部署qtm-api至sta環境並記錄經驗

## 日期

2025-06-18 13:19:48

## 需求背景

使用者回報上次對 `qtm-api` 服務在 `sta` 環境的部署失敗，要求重新執行一次部署流程。

## 執行動作

根據請求，執行了以下指令以觸發自動化部署流程：

```bash
./tools/cloud-deploy qtm-api sta develop --yes
```

此指令利用專案內的 `cloud-deploy` 腳本，自動化地將 `qtm-api` 倉庫的 `develop` 分支部署到 `sta` 環境，並透過 `--yes` 參數跳過手動確認。

## 執行結果

Cloud Build 管線被成功觸發，並順利完成所有部署步驟，包括：

1.  **原始碼拉取**：複製原始碼並初始化 Git Submodules。
2.  **依賴安裝**：執行 `composer install` 和 `npm install`。
3.  **前端建構**：執行 `vite build` 編譯前端資源。
4.  **打包與上傳**：將應用程式碼與環境設定檔壓縮並上傳至 Google Cloud Storage。
5.  **Kubernetes 部署**：
    *   更新 ConfigMaps。
    *   建立新的 Deployment (`app-v20250618121245`) 與 CronJob Deployment。
    *   採用 **V3 藍綠部署策略** 進行零停機流量切換。
    *   部署完成後，自動清理舊版本的 Deployment、HPA 等資源。

最終，服務成功更新至最新版本，部署流程順利完成。

## 經驗總結 (Lessons Learned)

此次成功的部署經驗，突顯了當前部署架構的幾個優點：

1.  **自動化部署流程的可靠性**：
    *   透過 `cloud-deploy` 腳本與 Google Cloud Build 整合，實現了「一鍵式」部署。這不僅提高了效率，也大幅降低了因手動操作可能引入的錯誤，確保了每次部署的一致性。

2.  **藍綠部署策略的穩健性**：
    *   部署流程採用了 V3 版的藍綠部署策略（Zero Downtime Blue-Green Deployment），此策略包含了深入的健康檢查、服務暖機時間、以及安全的流量切換機制。
    *   即使新版本有潛在問題，也能在流量切換前被健康檢查攔截，避免影響線上服務的穩定性，這是確保高品質交付的關鍵。

3.  **多階段驗證的重要性**：
    *   在部署前，腳本會自動檢查並顯示當前版本與即將部署版本的差異，提供明確的版本對照。
    *   在部署過程中，會等待新版本 Pod 完全就緒 (Ready) 並通過主動探測後，才進行流量切換。
    *   切換完成後，還會進行最終的服務驗證，確保新版本服務穩定。這些驗證步驟構成了完整的品質保證環節。

4.  **環境配置管理的有效性**：
    *   部署流程能自動識別目標環境（`sta`），並將對應的環境變數檔案（`env.sta`）打包進部署產物中。這確保了應用在不同環境（開發、測試、生產）中都能使用正確的配置，避免了配置錯誤導致的部署失敗。

綜上所述，一個自動化、具備穩健部署策略（如藍綠部署）、並包含多重驗證的 CI/CD 流程是現代軟體開發中不可或缺的一環。 