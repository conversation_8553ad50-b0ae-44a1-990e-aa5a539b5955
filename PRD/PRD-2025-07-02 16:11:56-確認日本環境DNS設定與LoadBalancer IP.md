# PRD - 確認日本環境 DNS 設定與 LoadBalancer IP

**建立日期**: 2025-07-02 16:11:56  
**狀態**: 已完成  
**優先級**: 高  

## 需求背景

用戶在部署日本環境 (jpn) 後，需要確認正確的 DNS A record 設定，特別是應該指向哪個 IP 地址。

## 問題描述

1. **LoadBalancer IP 確認**: 用戶發現 kubectl 輸出顯示的 EXTERNAL-IP 與部署指南文件中記錄的靜態 IP 不同
2. **DNS 設定疑問**: 不確定應該設定 DNS A record 到哪個 IP 地址
3. **文件不一致**: 部署指南中的 IP 資訊需要更新

## 技術資訊

### kubectl 輸出
```
NAME                     TYPE           CLUSTER-IP       EXTERNAL-IP    PORT(S)          AGE    SELECTOR             LABELS
service/app              LoadBalancer   **************   ************
```

### IP 地址資訊
- **LoadBalancer EXTERNAL-IP**: `************` (實際用於 DNS 設定)
- **原文件記錄的靜態 IP**: `*************` (IPv4)
- **IPv6 靜態 IP**: `2600:1901:0:4664::`

## 解決方案

### 1. DNS 設定確認
設定以下 DNS A records：
```
jp-qtm-api.qtmedical.com     A    ************
jp-dashboard.qtmedical.com   A    ************
```

### 2. 文件更新
更新 `docs/japan-deployment-guide.md` 文件，包含：

1. **DNS 配置章節**:
   - 明確指出使用 LoadBalancer EXTERNAL-IP
   - 添加具體的 IP 地址 (`************`)
   - 添加說明註記

2. **部署進度報告**:
   - 新增 LoadBalancer EXTERNAL-IP 資訊
   - 更新待完成步驟中的 IP 地址

3. **配置摘要**:
   - 添加 LoadBalancer EXTERNAL-IP 項目

## 技術說明

### LoadBalancer vs 靜態 IP
- **LoadBalancer 服務**: GKE 自動分配的 EXTERNAL-IP (`************`)
- **靜態 IP**: 預先創建的 IP 資源 (`*************`)
- **DNS 設定**: 應使用 LoadBalancer 的 EXTERNAL-IP

### 重要概念
1. LoadBalancer 服務會自動獲得一個外部 IP
2. 這個 IP 可能與預先分配的靜態 IP 不同
3. DNS 必須指向實際的 LoadBalancer EXTERNAL-IP
4. 如需使用特定靜態 IP，需在 LoadBalancer 配置中指定 `loadBalancerIP`

## 實施結果

### 已完成
- ✅ 確認正確的 DNS 設定 IP 地址
- ✅ 更新部署指南文件的 DNS 配置章節
- ✅ 更新部署進度報告中的 IP 資訊
- ✅ 添加技術說明和注意事項

### 後續行動
- [ ] 實際執行 DNS A record 設定 (需要域名管理權限)
- [ ] 驗證 DNS 解析是否正確
- [ ] 測試應用程式通過域名訪問

## 相關檔案

- `docs/japan-deployment-guide.md` - 主要部署指南
- `tools/deploy-eu` - 部署腳本
- `configmaps/src/repos/qtm-api/env.jpn` - 環境配置

## 學習要點

1. **LoadBalancer 服務 IP 管理**: 理解 GKE LoadBalancer 自動 IP 分配機制
2. **DNS 設定最佳實踐**: 確保 DNS 指向實際的服務端點
3. **文件維護**: 保持部署文件與實際配置同步
4. **IP 資源管理**: 區分靜態 IP 資源與服務分配的 IP

---

**負責人**: DevOps 團隊  
**審核人**: 系統管理員  
**最後更新**: 2025-07-02 16:11:56 