# PRD-2025-06-17 23:35:47-Cloud Build Substitution錯誤修復與零停機部署成功

## 需求背景

用戶在使用 `cloud-deploy qtm-api sta develop --deploy-strategy v3` 部署時遇到 Cloud Build substitution 錯誤：

```
ERROR: (gcloud.builds.submit) INVALID_ARGUMENT: generic::invalid_argument: invalid value for 'build.substitutions': key in the template "STRATEGY" is not a valid built-in substitution
```

## 問題分析

### 根本原因
在 `cloudbuild.yaml` 的第 606 行使用了：
```yaml
STRATEGY="${_STRATEGY}"
```

Cloud Build 解析器會將所有的 `${...}` 都當作 substitution 變數來處理，即使是在 shell 腳本中的變數賦值也會被誤認為是 substitution 模板，導致解析錯誤。

### 錯誤演進過程
1. 最初錯誤：`"DEPLOY_STRATEGY" is not a valid built-in substitution`
2. 修正後錯誤：`"STRATEGY" is not a valid built-in substitution`
3. 最終解決：直接在條件判斷中使用 substitution 變數

## 解決方案

### 技術修復
修改 `cloudbuild.yaml` 第 606-608 行：

**修改前：**
```yaml
STRATEGY="${_STRATEGY}"

if [ "$STRATEGY" = "v3" ]; then
```

**修改後：**
```yaml
if [ "${_STRATEGY}" = "v3" ]; then
```

### 修復原理
- 移除了中間變數賦值步驟
- 直接在 `if` 條件中使用 `${_STRATEGY}` substitution 變數
- 避免了 Cloud Build 解析器的錯誤識別

## 實施結果

### 🎯 部署成功摘要
- **部署策略**: V3（深度健康檢查）
- **總耗時**: 13分53秒
- **狀態**: ✅ 零停機部署成功

### 📊 版本資訊
- **舊版本**: `v20250617213822`
- **新版本**: `v20250617231939`
- **Git Commit**: `516cc8c7`
- **分支**: `develop`

### 🔄 零停機部署執行階段

#### Phase 1: 等待新部署就緒 ✅
- Deployment 成功創建並就緒 (1/1 replicas)

#### Phase 2: 深度健康檢查 ✅
- 所有容器通過 Readiness Probe
- Pod `app-v20250617231939-6886b55b9c-jrjpt` 所有容器已就緒

#### Phase 3: Service Endpoints 驗證 ⚠️
- 新 Pod 未能完全加入 Endpoints（有警告但不影響部署）

#### Phase 4: 應用層健康檢查 ✅
- Nginx 健康檢查通過
- PHP-FPM 健康檢查通過
- 應用健康狀態: 1/1

#### Phase 5: 安全流量切換 ✅
- 配置 Session Affinity
- 雙版本共存（新舊版本同時提供服務）
- 移除 Session Affinity
- 最終切換到新版本

#### Phase 6: 最終服務驗證 ✅
- 驗證成功率: 5/5 (100%)

#### Phase 7: 清理舊版本 ✅
- 舊版本資源清理完成

## 技術改進

### 1. Cloud Build 配置優化
- 修復了 substitution 變數解析錯誤
- 確保部署策略選擇功能正常運作

### 2. 零停機部署策略完善
- V3 策略提供最高級別的安全保障
- 深度健康檢查確保服務品質
- Session Affinity 避免連接中斷

### 3. 服務驗證機制
- 多次驗證確保服務穩定性
- 自動回滾機制（未觸發，部署成功）

## 驗證結果

### 服務狀態檢查
```bash
curl -s "https://sta-qtm-api.qtmedical.com/api/version"
```

**回應結果：**
```json
{
  "commit": "516cc8c7",
  "description": "2025-06-11 20:49:31 +0800",
  "ver": "develop"
}
```

### 部署工具使用
```bash
./tools/cloud-deploy qtm-api sta develop --deploy-strategy v3
```

## 後續建議

### 1. 持續監控
- 監控新版本的性能表現
- 關注 Service Endpoints 的穩定性

### 2. 文檔更新
- 更新部署文檔，說明 V3 策略的優勢
- 記錄 Cloud Build substitution 的最佳實踐

### 3. 自動化改進
- 考慮將 V3 策略設為所有環境的預設策略
- 完善 Endpoints 檢查機制

## 總結

這次修復成功解決了 Cloud Build substitution 錯誤，並且驗證了 V3 零停機部署策略的有效性。部署過程順利，服務穩定運行，為未來的部署提供了可靠的技術基礎。

**關鍵成果：**
- ✅ Cloud Build 配置錯誤修復
- ✅ 零停機部署成功實施
- ✅ 服務正常運行驗證
- ✅ 部署工具功能完善 