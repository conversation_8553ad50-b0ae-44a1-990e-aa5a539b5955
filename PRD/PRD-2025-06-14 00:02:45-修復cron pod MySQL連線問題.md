# PRD - 修復 Cron Pod MySQL 連線問題

## 問題描述

用戶回報 cron pod 無法連接到 MySQL 資料庫，導致佇列任務失敗。經過分析發現，cron pod 缺少 nginx 容器作為 MySQL 代理，而 nginx 在系統中扮演 TCP 代理的角色，將 localhost:3306 (寫入) 和 localhost:3307 (讀取) 的流量轉發到實際的 MySQL 資料庫服務。

## 根本原因分析

1. **架構差異**：app pod 有三個容器（php-fpm, nginx, cloudsql），而 cron pod 只有兩個容器（php-cron, cloudsql）
2. **缺少 MySQL 代理**：nginx 作為 TCP 代理，負責將本地端口轉發到 Cloud SQL 代理
3. **Cloud Build 配置問題**：`{{REPO_BRANCH}}` 變數未被正確替換
4. **健康檢查問題**：nginx 健康檢查嘗試訪問不存在的 PHP 端點

## 解決方案

### 1. 修改 Cron Pod 架構
- 在 `k8s-yaml/bundle/bundle.cron.template.yaml` 中添加 nginx 容器
- 添加 `init-permissions` init 容器設置 nginx 目錄權限
- 添加必要的 volumes：`nginx-cache`, `nginx-logs`, `nginx-run`

### 2. 修復 Cloud Build 配置
- 在 `cloudbuild.yaml` 中添加 `{{REPO_BRANCH}}` 變數替換：
  ```bash
  sed -i "s|{{REPO_BRANCH}}|${_REPO_REF}|g" /workspace/k8s-yaml/bundle/bundle.cron.yaml
  ```

### 3. 優化健康檢查
- 將 nginx 健康檢查從 HTTP 改為 TCP 檢查
- 檢查 MySQL 代理端口 3306 而不是 HTTP 端點
- 避免在 cron pod 中檢查不存在的 PHP-FPM 服務

### 4. ConfigMap 更新
- 確保包含所有必要的配置文件：
  ```bash
  kubectl create configmap configmaps \
    --from-file=configmaps/src/all/configs/ \
    --from-file=configmaps/src/all/scripts/ \
    --from-file=configmaps/src/all/secrets/ \
    --from-file=configmaps/src/repos/qtm-api/ \
    --from-file=configmaps/environments/sta.env
  ```

## 實施結果

### 成功部署
- 新的 cron pod 架構：`cron-v20250613233713-tcp`
- Pod 狀態：3/3 Running（php-cron + nginx + cloudsql）
- 健康檢查：TCP 檢查通過

### 功能驗證
✅ **MySQL 寫入連線**：`PDO('mysql:host=127.0.0.1;port=3306')` - 成功  
✅ **MySQL 讀取連線**：`PDO('mysql:host=127.0.0.1;port=3307')` - 成功  
✅ **Laravel 資料庫連線**：`DB::connection()->getPdo()` - 成功  
✅ **Cron 服務**：正常運行並配置完成  

### 清理工作
- 刪除失敗的部署：`cron-v20250613233713`, `cron-v20250613233713-fixed`
- 保留成功的部署：`cron-v20250613233713-tcp`

## 技術細節

### Nginx 配置
- **TCP 代理**：將 localhost:3306 轉發到寫入資料庫，localhost:3307 轉發到讀取資料庫
- **狀態端點**：`/zxcasdqwezxcasdqwezxcasdqwenginxstatus` 用於監控
- **配置文件**：使用 `/configmaps/nginx.conf` 和 `/configmaps/nginx.server.conf`

### 容器架構
```yaml
containers:
  - name: php-cron        # Laravel 應用和 cron 任務
  - name: nginx           # TCP 代理和 HTTP 服務
  - name: cloudsql        # Cloud SQL 代理
initContainers:
  - name: init-sysctl     # 系統參數設置
  - name: init-permissions # nginx 目錄權限設置
```

### 健康檢查策略
```yaml
livenessProbe:
  tcpSocket:
    port: 3306          # 檢查 MySQL 代理端口
readinessProbe:
  tcpSocket:
    port: 3306          # 檢查 MySQL 代理端口
```

## 預防措施

1. **模板同步**：確保 cron 和 app 模板的容器架構保持一致
2. **變數檢查**：在 Cloud Build 中驗證所有模板變數都被正確替換
3. **健康檢查**：根據容器實際功能設計適當的健康檢查
4. **測試流程**：在部署前驗證 MySQL 連線和基本功能

## 影響範圍

- **修復範圍**：所有環境的 cron pod 部署
- **向下相容**：不影響現有 app pod 功能
- **部署策略**：使用版本化部署名稱避免衝突

## 後續工作

1. 監控 cron 任務執行狀況
2. 驗證佇列任務處理是否恢復正常
3. 考慮將修復應用到其他環境（pro, aus, fra）
4. 更新部署文檔和最佳實踐指南 