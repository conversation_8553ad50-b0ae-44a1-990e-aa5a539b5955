# 修復 Cron Pod 的 MySQL 連線問題

**日期:** $(date +"%Y-%m-%d %H:%M:%S")

## 問題描述

`cron` pod 在執行背景任務時，因無法連線到 MySQL 資料庫而導致隊列中的任務（queue job）失敗。

## 問題分析

經過比對，發現 `cron` pod 的運行環境與可正常連線資料庫的 `php` pod 相比，缺少了 Nginx 服務。在目前的架構中，Nginx 不僅處理 HTTP 請求，還作為 TCP 代理，轉發 `localhost:3306` (寫入) 和 `localhost:3307` (讀取) 的流量到實際的 MySQL 資料庫服務。

由於 `cron` pod 中沒有運行 Nginx，當 Laravel 的 queue worker 嘗試透過 `localhost` 連線資料庫時，連線請求失敗，導致任務無法執行。

## 解決方案

為了解決這個問題，我們對 `cron` pod 的啟動腳本進行了修改，使其包含 Nginx 服務。

**具體變更:**

1.  **修改檔案:** `configmaps/src/all/scripts/init_cron.sh`

2.  **變更內容:**
    *   在 `update_configs` 函數中，增加了一行指令，將 `nginx.conf` 設定檔複製到容器內的 `/etc/nginx/` 目錄。
    *   在 `main` 函數中，於啟動 `horizon` 和 `queue:work` 服務之前，加入了 `nginx` 指令來啟動 Nginx 服務。

## 預期結果

經過以上修改後，`cron` pod 的運行環境將包含 Nginx 代理，使其能夠像 `php` pod 一樣成功連線到 MySQL 資料庫。這將解決 queue job 失敗的問題，確保背景任務能正常執行。 