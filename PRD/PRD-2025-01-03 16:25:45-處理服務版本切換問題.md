# PRD - 處理服務版本切換問題

**時間：** 2025-01-03 16:25:45  
**需求類型：** 緊急運維操作  
**狀態：** 已完成  

## 問題背景

用戶反映新版本有問題，需要將流量切回舊版 v20250523120737。

## 用戶需求

```bash
kubectl patch svc app \
  -p '{"spec": {"selector": {"app": "qtm-api", "env": "fra", "version": "v20250523120737"}}}'
```

將服務流量從當前版本切換到舊版 v20250523120737。

## 問題發現與分析

### 初始狀態
- 當前運行版本：v20250620153811
- 目標切換版本：v20250523120737
- 運行環境：fra (法國區域)

### 發現的問題
1. 執行切換命令後，服務選擇器成功更新到 v20250523120737
2. 但查檢發現該版本沒有運行中的 Pod
3. 這導致服務無法找到匹配的後端，造成流量中斷

### 根本原因
目標版本 v20250523120737 的部署(Deployment)不存在或已被清理，只有當前版本 v20250620153811 在運行。

## 解決方案

### 方案選擇
經過與用戶確認，選擇將服務切回到目前正在運行的版本 v20250620153811，而不是重新部署舊版本。

### 執行步驟

1. **服務版本回滾**
```bash
kubectl patch svc app \
  -p '{"spec": {"selector": {"app": "qtm-api", "env": "fra", "version": "v20250620153811"}}}'
```

2. **狀態驗證**
```bash
# 檢查服務選擇器
kubectl get svc app -o yaml | grep -A 5 selector

# 檢查運行中的 Pod
kubectl get pods -l app=qtm-api,env=fra --show-labels

# 檢查服務端點
kubectl get endpoints app
```

## 最終結果

✅ **服務恢復正常**
- 服務選擇器：指向 v20250620153811
- 運行中的 Pod：3 個實例正常運行
- 服務端點：6 個端點可用
- 流量路由：正常處理請求

## 經驗總結

### 操作流程優化建議
1. **版本切換前檢查**：確認目標版本的部署是否存在和運行
2. **分階段切換**：先檢查 Pod 狀態，再執行服務切換
3. **監控驗證**：切換後立即檢查端點和服務狀態

### 相關命令參考
```bash
# 檢查特定版本的 Pod
kubectl get pods -l app=qtm-api,env=fra,version=<VERSION>

# 檢查所有相關部署
kubectl get deployments -l app=qtm-api,env=fra

# 檢查服務端點狀態
kubectl get endpoints <SERVICE_NAME>
```

## 技術細節

### Kubernetes 選擇器機制
- 服務使用標籤選擇器匹配 Pod
- 如果沒有匹配的 Pod，服務端點為空
- 這會導致所有流量無法路由到後端

### 版本管理策略
- 保持多版本部署並存的重要性
- 藍綠部署或金絲雀部署的考慮
- 版本清理政策的制定

## 後續建議

1. **版本保留策略**：建議保留最近 2-3 個版本的部署
2. **切換腳本**：開發自動化腳本，包含預檢查機制
3. **監控告警**：設置服務端點數量監控
4. **文檔更新**：更新版本切換操作手冊 