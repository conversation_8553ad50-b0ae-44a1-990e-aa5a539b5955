### 標題：修復 Nginx 健康檢查設定導致的 OOM (記憶體超限) 錯誤

### 日期：2025-06-18

### 1. 問題描述

使用者要求部署代號為 `v3.8` 的新版本以測試修正，但部署在 Cloud Build 的步驟 #8 (`Update App YAML`) 失敗。錯誤訊息顯示 `kubectl rollout status` 等待逾時，意味著新的應用程式 Pod 無法正常啟動。

### 2. 偵錯過程

1.  **初步調查**：
    *   連接到 `sta-qtm-api` GKE 叢集。
    *   使用 `kubectl get pods` 發現新的應用程式 Pod `app-v20250618115505-....` 狀態為 `2/3 Running` 且不斷重啟。

2.  **深入分析**：
    *   使用 `kubectl describe pod` 鎖定問題 Pod，發現是 `nginx` 容器無法準備就緒 (`Ready: False`)。
    *   關鍵線索：
        *   **退出碼 137**：`nginx` 容器最後的退出碼為 137，這在 Linux 環境中通常表示因 OOM (Out of Memory) 而被系統的 OOM Killer 強制終止。
        *   **健康檢查失敗**：Liveness 和 Readiness 探測都失敗，日誌事件顯示訪問 `http://:80/api/healthz` 時得到 `404 Not Found` 回應。
        *   **日誌缺失**：嘗試使用 `kubectl logs` 查看當前和上一個 `nginx` 容器的日誌，均無任何輸出。

3.  **定位根源**：
    *   由於沒有日誌，問題矛頭指向 Nginx 啟動前的環節。我們檢查了 Nginx 的啟動腳本 `init_nginx.sh`，發現它很簡單，只負責載入設定檔。
    *   因此，問題必定出在 Nginx 的設定檔上。我們依序檢查了 `nginx.conf.tmpl` 和它所引用的 `nginx.server.conf`。
    *   **最終發現**：在 `nginx.server.conf` 中，完全沒有為 `/api/healthz` 這個路徑設定 `location` 區塊。

### 3. 問題根源

1.  **404 錯誤**：因為缺少對 `/api/healthz` 的直接處理規則，所有健康檢查請求都被 Nginx 轉發給後端的 PHP-FPM (Laravel) 處理。由於 Laravel 應用中沒有定義此路由，因此返回 404。
2.  **OOM 崩潰**：每一次健康檢查都會啟動一次完整的 Laravel 框架來處理這個註定失敗的請求。在高頻率的探測下（每 5-10 秒一次），這種重量級的操作迅速耗盡了分配給 `nginx` 容器的 512Mi 記憶體，導致其被系統 OOM Kill，從而引發了 Pod 的不斷重啟。

### 4. 解決方案

在 `configmaps/src/repos/qtm-api/nginx.server.conf` 檔案中，為健康檢查路徑新增一個專用的 `location` 區塊，讓 Nginx 可以直接、輕量地回應，而無需驚動後端應用：

```nginx
location /api/healthz {
    access_log off;
    return 200 "OK";
    add_header Content-Type text/plain;
}
```

### 5. 驗證

該修正已應用，並重新觸發了一次新的部署。部署程序交由使用者在背景監控。 