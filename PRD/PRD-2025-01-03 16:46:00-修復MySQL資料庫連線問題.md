# PRD - 修復 MySQL 資料庫連線問題

**時間：** 2025-01-03 16:46:00  
**需求類型：** 緊急故障修復  
**狀態：** 已完成  

## 問題背景

用戶反映應用程式出現 500 錯誤，具體錯誤訊息：
```
SQLSTATE[HY000] [2006] MySQL server has gone away (Connection: mysql, SQL: select * from `users` where `email` = <EMAIL> and `users`.`deleted_at` is null limit 1)
```

## 問題分析與診斷

### 初始調查

1. **錯誤模式**：MySQL 連線中斷，提示 "MySQL server has gone away"
2. **影響範圍**：應用程式無法執行資料庫查詢，導致 500 錯誤
3. **環境信息**：fra 環境，版本 v20250620163756

### 深度診斷

#### Cloud SQL Proxy 狀態
```bash
kubectl logs app-v20250620163756-c4f989f8-6zdlq -c cloudsql
```
**發現**：Cloud SQL Proxy 正常運行，但使用 **TCP 端口**而不是 Unix socket：
- 監聽：`127.0.0.1:33061` (fra-prod-db)
- 監聽：`127.0.0.1:33071` (fra-prod-db-replica)

#### nginx 配置問題
```bash
kubectl exec pod -c nginx -- cat /configmaps/nginx.conf
```
**發現**：nginx 配置嘗試連接不存在的 Unix socket 路徑

#### 服務架構問題
**發現**：應用配置使用 `DB_HOST=mysql`，但集群中沒有名為 `mysql` 的服務

## 根本原因

1. **Cloud SQL Proxy 版本變化**：
   - 舊版本：使用 Unix socket (`/cloudsql/instance-name`)
   - 新版本：使用 TCP 端口 (`127.0.0.1:33061`, `127.0.0.1:33071`)

2. **nginx 配置不匹配**：
   - 配置嘗試連接 Unix socket
   - 實際需要連接 TCP 端口

3. **缺少 mysql 服務**：
   - 應用配置期望 `mysql` hostname
   - 集群中沒有對應的 Kubernetes 服務

## 解決方案

### 1. 修正 nginx 配置

**文件**：`configmaps/src/all/configs/nginx.conf`

**修改**：
```nginx
stream {
    upstream mysql_write {
        server 127.0.0.1:33061;  # 使用 TCP 端口
        # server unix:/cloudsql/... # 註解掉 Unix socket
    }

    upstream mysql_read {
        server 127.0.0.1:33071 max_conns=512;  # 使用 TCP 端口
        # server unix:/cloudsql/... # 註解掉 Unix socket
    }

    server {
        listen 3306;
        proxy_pass mysql_write;
    }

    server {
        listen 3307 so_keepalive=on;
        proxy_pass mysql_read;
    }
}
```

### 2. 更新 ConfigMap

```bash
# 複製更新的配置
cp -f configmaps/src/all/configs/nginx.conf configmaps/dist/

# 更新 ConfigMap
kubectl delete cm configmaps
kubectl create cm configmaps --from-file=configmaps/dist/ -o yaml --dry-run=client | kubectl apply -f -

# 重新啟動部署
kubectl rollout restart deployment app-v20250620163756
```

### 3. 創建 mysql 服務

**目的**：提供 `mysql` hostname 讓應用程式連接

```yaml
apiVersion: v1
kind: Service
metadata:
  name: mysql
  labels:
    app: mysql-proxy
spec:
  ports:
  - port: 3306
    targetPort: 3306
    protocol: TCP
  selector:
    env: fra
    version: v20250620163756
```

## 連接流程

**修復後的資料庫連接流程**：
```
Laravel 應用
    ↓ (mysql:3306)
mysql 服務
    ↓ (*********:3306)
Pod nginx 容器
    ↓ (127.0.0.1:3306 → proxy_pass mysql_write)
    ↓ (127.0.0.1:33061)
Cloud SQL Proxy 容器
    ↓ (TCP connection)
Google Cloud SQL (fra-prod-db)
```

## 驗證結果

### 連接測試
```bash
# 測試直接 TCP 連接
kubectl exec pod -c php-fpm -- php -r 'new PDO("mysql:host=127.0.0.1;port=33061", "root", "password");'
# ✅ SUCCESS

# 測試 nginx 代理
kubectl exec pod -c php-fpm -- php -r 'new PDO("mysql:host=127.0.0.1;port=3306", "root", "password");'
# ✅ SUCCESS

# 測試 mysql 服務
kubectl exec pod -c php-fpm -- php -r 'new PDO("mysql:host=mysql;port=3306", "root", "password");'
# ✅ SUCCESS
```

### 服務狀態
```bash
kubectl get endpoints mysql
# NAME    ENDPOINTS        AGE
# mysql   *********:3306   2m

kubectl get svc app -o yaml | grep version
# version: v20250620163756

kubectl get pods -l version=v20250620163756
# NAME                                   READY   STATUS    RESTARTS   AGE
# app-v20250620163756-687849df7f-sdwhc   3/3     Running   0          8m
```

## 經驗總結

### 技術學習

1. **Cloud SQL Proxy 版本差異**：
   - 新版本預設使用 TCP 端口而非 Unix socket
   - 需要根據實際 Proxy 配置調整 nginx 設置

2. **Kubernetes 服務發現**：
   - 應用程式依賴特定的 hostname（如 `mysql`）
   - 需要確保相應的 Kubernetes 服務存在

3. **容器間通信**：
   - 同一 Pod 內的容器可以通過 localhost 通信
   - nginx stream 模組可以代理 TCP 連接

### 故障排除流程

1. **日誌分析**：檢查應用程式、代理和資料庫容器日誌
2. **連接測試**：從簡單到複雜逐步測試連接
3. **配置驗證**：確認配置文件與實際運行環境一致
4. **服務發現**：驗證 Kubernetes 服務和端點

### 預防措施

1. **監控告警**：
   - 設置資料庫連接錯誤監控
   - Cloud SQL Proxy 健康檢查

2. **配置管理**：
   - 版本化管理 ConfigMap 變更
   - 建立配置變更測試流程

3. **文檔更新**：
   - 記錄當前架構和連接流程
   - 更新故障排除手冊

## 後續行動

1. **✅ 已完成**：修復當前故障
2. **建議執行**：
   - 建立 Cloud SQL 連接監控
   - 更新部署文檔
   - 考慮設置資料庫連接池配置
   - 評估是否需要多區域備援配置 