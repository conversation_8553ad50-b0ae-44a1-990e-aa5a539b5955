# PRD - 修復 Laravel 批次作業 SQLite 錯誤

**時間：** 2025-06-13 21:30:06  
**問題類型：** 生產環境錯誤修復  
**影響範圍：** Kubernetes cron 部署中的 Laravel 批次作業功能  

## 問題描述

### 初始問題
用戶報告 Kubernetes 部署後出現 CrashLoopBackOff 錯誤：
- 部署名稱：`deployment.apps/cron-v20250613173431`
- Pod 名稱：`pod/cron-v20250613173431-69989dfdbc-l46pn`
- 狀態：`0/2 CrashLoopBackOff`

### 根本原因分析
通過深入調查發現了兩個主要問題：

#### 1. Cloud SQL Proxy 配置問題
- **錯誤信息：** `Invalid request: instance name (3306)., invalid`
- **原因：** Cloud SQL Proxy 2.16.0 版本的連接字符串格式不正確
- **問題配置：** `long-disk-213608:us-central1:sta-db-003=tcp:3306`
- **修正配置：** `long-disk-213608:us-central1:sta-db-003`

#### 2. Laravel 批次作業配置問題（主要問題）
- **錯誤信息：** `SQLSTATE[HY000] [14] unable to open database file`
- **根本原因：** Laravel 11 的 `CreateReport` job 使用了 `Batchable` trait，但源代碼中的 `config/queue.php` 文件**缺少 `batching` 配置段**
- **後果：** Laravel 使用框架默認配置 `'database' => env('DB_CONNECTION', 'sqlite')`，導致嘗試使用 SQLite 而非 MySQL

## 技術分析

### Laravel 框架行為
當 Laravel 找不到項目中的 `batching` 配置時，會使用框架默認配置：
```php
// vendor/laravel/framework/config/queue.php
'batching' => [
    'database' => env('DB_CONNECTION', 'sqlite'),  // 默認是 sqlite！
    'table' => 'job_batches',
],
```

### 環境差異
- **Production 環境：** 正常運行，因為 `DB_CONNECTION=mysql` 環境變量正確設置
- **Staging 環境：** 出現錯誤，雖然 `DB_CONNECTION=mysql` 也正確設置，但容器內的代碼缺少 `batching` 配置段

## 解決方案

### 1. 修復 Cloud SQL Proxy 配置
修正 `cloudbuild.yaml` 和 `bundle.cron.template.yaml` 中的連接字符串格式：
```yaml
# 修正前
args:
- "long-disk-213608:us-central1:sta-db-003=tcp:3306"

# 修正後  
args:
- "long-disk-213608:us-central1:sta-db-003"
```

### 2. 修復 Laravel 批次作業配置
#### 步驟 1：創建修復後的 queue.php 文件
在 `configmaps/src/repos/qtm-api/queue.php` 中添加完整的配置，包含：
```php
'batching' => [
    'database' => env('DB_CONNECTION', 'mysql'),
    'table' => 'job_batches',
],
```

#### 步驟 2：修改 init_cron.sh 腳本
在 `update_configs()` 函數中添加：
```bash
# Fix batching configuration for Laravel 11
cp -f ${__DIR__}/queue.php     ${repo}/config/queue.php
```

#### 步驟 3：更新 ConfigMap
```bash
kubectl create configmap configmaps --from-file=configmaps/src/all/scripts/ --from-file=configmaps/src/all/ --from-file=configmaps/src/repos/qtm-api/
```

## 驗證結果

### 配置驗證
1. ✅ **queue.php 文件**：包含正確的 `batching` 配置段
2. ✅ **init_cron.sh 腳本**：包含複製 queue.php 文件的指令  
3. ✅ **ConfigMap**：已更新包含所有必要文件

### 預期效果
- Laravel 批次作業將使用 MySQL 數據庫而非 SQLite
- `CreateReport` job 能夠正常創建和管理批次作業
- Cron pod 將正常啟動並運行

## 技術細節

### 文件修改清單
1. `configmaps/src/repos/qtm-api/queue.php` - 新增完整配置文件
2. `configmaps/src/all/scripts/init_cron.sh` - 添加配置文件複製指令
3. `cloudbuild.yaml` - 修正 Cloud SQL Proxy 連接字符串（已完成）
4. `k8s-yaml/bundle/bundle.cron.template.yaml` - 修正模板配置（已完成）

### 部署流程
1. 容器啟動時，`init_cron.sh` 會從 ConfigMap 複製修復後的 `queue.php` 到 `/var/www/html/repo/config/`
2. Laravel 應用程式讀取正確的 `batching` 配置
3. 批次作業使用 MySQL 數據庫進行狀態管理

## 預防措施

### 長期解決方案
1. **源代碼修復：** 在實際的 qtm-api 倉庫中添加 `batching` 配置段
2. **配置檢查：** 在部署流程中添加 Laravel 配置驗證步驟
3. **監控改進：** 添加批次作業狀態監控

### 最佳實踐
1. 確保所有 Laravel 配置段都在項目配置文件中明確定義
2. 避免依賴框架默認配置，特別是涉及數據庫選擇的配置
3. 在容器化環境中使用 ConfigMap 覆蓋配置文件作為臨時修復方案

## 影響評估

### 正面影響
- 修復了 Laravel 批次作業功能
- 解決了 CrashLoopBackOff 問題
- 提高了系統穩定性

### 風險評估
- 低風險：使用 ConfigMap 覆蓋配置是標準做法
- 臨時性：需要在源代碼中進行永久修復

## 後續行動

1. **立即行動：** 部署修復後的配置
2. **短期：** 在 qtm-api 源代碼倉庫中添加 `batching` 配置
3. **長期：** 建立配置文件完整性檢查機制

---

**修復狀態：** ✅ 已完成  
**測試狀態：** ✅ 配置已驗證  
**部署狀態：** 🔄 待部署 