# k8s狀態顯示工具優化AGE和標籤顯示

## 需求描述

用戶要求對 `tools/k8s__show_status` 腳本進行優化，主要包括：

1. **AGE 顯示改進**：將時間戳改為相對時間顯示（如 "2小時前"）
2. **增加欄位顯示**：添加 SELECTOR 和 LABELS 欄位到各個資源的顯示中
3. **簡化節點狀態顯示**：移除不需要的 KERNEL-VERSION 和 CONTAINER-RUNTIME 欄位
4. **優化選擇器顯示**：移除多餘的 `map[]` 字詞，讓顯示更簡潔

## 技術實現

### 最終解決方案

經過多次調整，最終採用以下方案：

1. **添加 shebang**：在腳本開頭添加 `#!/bin/bash` 以消除 linter 警告

2. **AGE 顯示優化**：
   - 使用 kubectl 標準格式而非 custom-columns，這樣可以自動獲得相對時間顯示（如 "2d", "5h", "30m"）
   - 所有資源（節點、部署、Pod、服務、Ingress、HPA）都顯示相對時間

3. **SELECTOR 和 LABELS 欄位添加**：
   - 將選擇器資訊獨立成一個區塊顯示
   - 在分頁/滾動模式中添加完整的標籤顯示區塊
   - 在 watch 模式中簡化顯示以避免螢幕過於擁擠

4. **選擇器顯示優化**：
   - 使用 `sed` 命令移除 `map[]` 字詞：`sed 's/map\[\([^]]*\)\]/\1/g'`
   - 讓選擇器顯示更加簡潔，從 `map[env:sta version:v20250617142039]` 變為 `env:sta version:v20250617142039`

### 問題解決歷程

**遇到的問題**：
1. `--show-labels` 參數不能與 `custom-columns` 同時使用
2. custom-columns 中的 AGE 顯示為時間戳而非相對時間
3. 節點狀態顯示過多不需要的欄位
4. 選擇器顯示包含多餘的 `map[]` 字詞

**解決方案**：
1. 放棄使用 custom-columns，改用 kubectl 標準格式
2. 將選擇器和標籤資訊分離到獨立區塊
3. 針對不同模式提供不同的詳細程度
4. 使用 sed 正則表達式清理選擇器輸出格式

### 最終顯示欄位

**節點狀態**：
- 使用 `kubectl get nodes` 標準格式：名稱、狀態、角色、年齡、版本

**部署狀態**：
- 使用 `kubectl get deploy` 標準格式：名稱、就緒/期望、最新、可用、年齡

**Pod 狀態**：
- 使用 `kubectl get pods -o wide`：名稱、就緒、狀態、重啟、年齡、IP、節點、提名節點、讀就緒門

**服務狀態**：
- 使用 `kubectl get svc -o wide`：名稱、類型、叢集IP、外部IP、端口、年齡、選擇器

**Ingress 狀態**：
- 使用 `kubectl get ing` 標準格式：名稱、類別、主機、地址、端口、年齡

**HPA 狀態**：
- 使用 `kubectl get hpa` 標準格式：名稱、參考、目標、最小Pod、最大Pod、副本、年齡

**選擇器資訊**：
- 部署選擇器：獨立區塊顯示，格式清理後（如：`env:sta version:v20250617142039`）
- 服務選擇器：獨立區塊顯示，格式清理後

**標籤資訊**：
- 在分頁/滾動模式中使用 `--show-labels` 顯示所有資源標籤

### 模式差異

**Watch 模式**（預設）：
- 簡化顯示，適合即時監控
- 選擇器資訊限制顯示行數，使用清理後的格式

**Page/Scroll 模式**：
- 完整顯示，包含詳細選擇器和標籤資訊
- 選擇器格式經過清理，移除 `map[]` 字詞

**Full 模式**：
- 維持原始的完整顯示格式

## 實施結果

- ✅ 修復了 linter 警告（添加 shebang）
- ✅ 所有資源都正確顯示相對時間（2d, 5h, 30m 格式）
- ✅ 成功添加了 SELECTOR 和 LABELS 欄位顯示
- ✅ 移除了節點狀態中不需要的欄位
- ✅ 解決了 `--show-labels` 與 `custom-columns` 的衝突問題
- ✅ 針對不同使用場景優化了顯示內容
- ✅ 優化了選擇器顯示格式，移除多餘的 `map[]` 字詞
- ✅ 所有模式（預設 watch、page、scroll、full）都已更新
- ✅ 幫助文字已更新以反映新的欄位顯示

## 測試驗證

建議用戶執行以下命令測試：
```bash
# 測試預設 watch 模式（即時監控）
./tools/k8s__show_status

# 測試分頁模式（詳細分析）
./tools/k8s__show_status --page

# 測試說明
./tools/k8s__show_status --help
```

## 技術亮點

1. **適應性設計**：不同模式提供不同詳細程度的資訊
2. **效能優化**：watch 模式簡化顯示以減少螢幕閃爍
3. **使用者體驗**：保持 kubectl 原生格式的一致性
4. **資訊完整性**：確保選擇器和標籤資訊不遺失
5. **格式優化**：使用 sed 清理輸出，提供更簡潔的選擇器顯示

## 完成時間

2025-06-17 14:51:24

## 更新記錄

- 14:55 - 修正 `--show-labels` 與 `custom-columns` 衝突問題
- 15:00 - 最終方案：使用標準 kubectl 格式確保相對時間顯示
- 15:05 - 優化顯示內容，移除不必要欄位，分離選擇器資訊
- 15:10 - 優化選擇器格式顯示，使用 sed 移除 `map[]` 字詞 