# PRD - 修復網站 500 錯誤和 Ingress 路由問題

**時間：** 2025-01-03 17:02:30  
**需求類型：** 緊急故障修復  
**狀態：** 已完成  

## 問題背景

用戶反映網站 https://fr-dashboard.qtmedical.com/ 無法訪問，出現以下錯誤：
```
Error: Server Error
The server encountered a temporary error and could not complete your request.
Please try again in 30 seconds.
```

這是在先前修復資料庫連線問題之後發現的新問題。

## 問題分析與診斷

### 初始狀態檢查

#### 1. Pod 狀態
```bash
kubectl get pods -l version=v20250620163756
# NAME                                   READY   STATUS    RESTARTS   AGE
# app-v20250620163756-687849df7f-sdwhc   3/3     Running   0          6m44s
```
**結果**：Pod 正常運行，3/3 Ready

#### 2. 應用程式日誌
```bash
kubectl logs app-v20250620163756-687849df7f-sdwhc -c php-fpm --tail=20
```
**結果**：PHP-FPM 正常啟動，Horizon 也正常運行

#### 3. Nginx 日誌
**發現**：只有健康檢查請求，沒有外部流量到達

### 深度診斷 - Ingress 問題

#### Ingress 狀態檢查
```bash
kubectl describe ingress fra-prod-ingress-with-tls-https
```

**發現的問題**：
1. **後端狀態**：`app:80 (<none>)` - 沒有端點
2. **SSL 證書錯誤**：多個證書遺失警告
3. **Load Balancer 同步錯誤**

#### 服務端點檢查
```bash
kubectl get endpoints app
# NAME   ENDPOINTS   AGE
# app    <none>      2y180d
```
**根本問題**：app 服務沒有端點！

### 根本原因分析

#### 服務選擇器配置錯誤
```bash
kubectl get svc app -o jsonpath='{.spec.selector}' | jq .
{
  "app": "qtm-api",     # ❌ Pod 沒有這個標籤
  "env": "fra",         # ✅ Pod 有這個標籤  
  "version": "v20250620163756"  # ✅ Pod 有這個標籤
}
```

#### Pod 實際標籤
```bash
kubectl get pod app-v20250620163756-687849df7f-sdwhc --show-labels
# LABELS: env=fra,pod-template-hash=687849df7f,version=v20250620163756
```

**問題確認**：服務選擇器要求 `app=qtm-api` 標籤，但 Pod 沒有這個標籤。

## 根本原因

1. **服務選擇器不匹配**：
   - 服務要求：`app=qtm-api, env=fra, version=v20250620163756`
   - Pod 實際：`env=fra, version=v20250620163756`（缺少 `app=qtm-api`）

2. **部署標籤配置**：
   - 部署的 Pod 模板沒有設置 `app` 標籤
   - 所有版本的部署都只有 `env` 和 `version` 標籤

3. **Ingress 連鎖反應**：
   - 無端點 → Ingress 後端不健康 → Load Balancer 無法路由流量

## 解決方案

### 1. 修正服務選擇器

**問題**：服務選擇器包含 Pod 沒有的標籤  
**解決**：移除 `app=qtm-api` 標籤，只保留實際存在的標籤

#### 操作步驟
```bash
# 導出當前服務配置
kubectl get svc app -o yaml > app-service.yaml

# 編輯配置，移除 app 標籤
# selector:
#   env: fra
#   version: v20250620163756

# 刪除並重新創建服務
kubectl delete svc app
kubectl apply -f app-service.yaml
```

### 2. 驗證修復

#### 檢查端點
```bash
kubectl get endpoints app
# NAME   ENDPOINTS                    AGE
# app    *********:80,*********:443   5s
```
**結果**：✅ 端點出現

#### 檢查選擇器
```bash
kubectl get svc app -o jsonpath='{.spec.selector}' | jq .
{
  "env": "fra",
  "version": "v20250620163756"
}
```
**結果**：✅ 選擇器正確

#### 檢查 Ingress 後端
```bash
kubectl describe ingress fra-prod-ingress-with-tls-https | grep -A 5 "Backends"
# *           *     app:80 (*********:80)
# ingress.kubernetes.io/backends: {"k8s1-5a599395-default-app-80-f12564e6":"HEALTHY"}
```
**結果**：✅ 後端健康

### 3. 測試網站訪問

#### 內部測試
```bash
kubectl exec pod -c nginx -- curl -s -o /dev/null -w "%{http_code}" http://localhost/api/version
# 200
```

#### 外部測試
```bash
curl -I https://fr-dashboard.qtmedical.com/
# HTTP/2 302 
# location: https://fr-dashboard.qtmedical.com/login
```

**結果**：✅ 網站正常，返回 302 重定向到登入頁面

## 修復結果

### 網站狀態
- **URL**：https://fr-dashboard.qtmedical.com/
- **HTTP 狀態**：302 (正常重定向)
- **SSL**：正常工作
- **應用程式**：Laravel 正常響應
- **資料庫**：連接正常

### 服務狀態
```bash
# 服務端點
kubectl get endpoints app
# NAME   ENDPOINTS                    AGE
# app    *********:80,*********:443   

# Ingress 後端
kubectl describe ingress fra-prod-ingress-with-tls-https
# app:80 (*********:80) - HEALTHY
```

### 完整流量路徑
```
用戶請求 → Google Load Balancer → Ingress → app 服務 → Pod (nginx + php-fpm)
                                            ↓
                                    ✅ 端點：*********:80,443
```

## 經驗總結

### 技術學習

1. **Kubernetes 服務發現**：
   - 服務選擇器必須與 Pod 標籤精確匹配
   - 多餘的選擇器標籤會導致無端點
   - 端點是服務路由的基礎

2. **Ingress 依賴鏈**：
   - Ingress → Service → Endpoints → Pods
   - 任何一環斷裂都會導致流量無法到達

3. **故障診斷順序**：
   - Pod 狀態 → 應用日誌 → 服務端點 → Ingress 後端

### 故障排除流程

1. **確認 Pod 健康**：檢查 Pod 狀態和應用日誌
2. **檢查服務端點**：`kubectl get endpoints`
3. **驗證標籤匹配**：比較服務選擇器和 Pod 標籤
4. **檢查 Ingress 狀態**：確認後端健康狀態
5. **測試流量路徑**：從內部到外部逐步測試

### 預防措施

1. **標籤標準化**：
   - 建立一致的標籤命名規範
   - 確保部署模板包含所有必要標籤

2. **監控告警**：
   - 設置服務端點數量監控
   - Ingress 後端健康狀態告警

3. **部署驗證**：
   - 部署後自動檢查端點狀態
   - 包含端到端健康檢查

## 後續行動

1. **✅ 已完成**：修復當前故障
2. **建議執行**：
   - 檢查其他環境的服務選擇器配置
   - 建立標籤標準化文檔
   - 設置端點監控告警
   - 更新部署流程檢查清單

## 相關文件

- [資料庫連線修復](PRD-2025-01-03 16:46:00-修復MySQL資料庫連線問題.md)
- [服務版本切換](PRD-2025-01-03 16:25:45-處理服務版本切換問題.md) 