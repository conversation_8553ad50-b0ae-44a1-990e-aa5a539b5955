# PRD - 修復 GitLab 子模塊認證問題

## 問題描述

在 Cloud Build 部署過程中，GitLab 子模塊無法正確克隆，出現以下錯誤：

```
fatal: clone of 'https://gitlab.qtmedical.com/DataService/qtm-cointerpretation.git' into submodule path '/workspace/repos/qtm-api/app/Jobs/qtm-cointerpretation' failed
fatal: could not read Username for 'https://gitlab.qtmedical.com': No such device or address
```

## 根本原因分析

1. **認證配置問題**：雖然已經配置了 GitLab token 的全域 URL 重寫規則，但 Git submodule 在更新時沒有正確應用這些配置
2. **URL 格式不一致**：submodule 的原始 URL 格式與認證配置的 URL 重寫規則不完全匹配
3. **認證方式**：需要使用 `gitlab-ci-token` 格式而不是 `oauth2` 格式

## 解決方案

### 修改內容

在 `cloudbuild.yaml` 的 "Setup Git and Submodules" 步驟中，添加手動更新 submodule URL 的邏輯：

```bash
# 手動更新每個 submodule 的 URL 以確保使用正確的認證
echo "Updating submodule URLs to use authentication..."
git config submodule.app/Jobs/qtm-cointerpretation.url "https://gitlab-ci-token:$$<EMAIL>/DataService/qtm-cointerpretation.git"
git config submodule.app/Jobs/qtm-analysis.url "https://gitlab-ci-token:$$<EMAIL>/Analysis/Measurement.git"
git config submodule.app/Jobs/hrv.url "https://gitlab-ci-token:$$<EMAIL>/Analysis/hrv.git"

echo "Updated submodule URLs:"
git config --list | grep -E "submodule\..*\.url" || echo "No submodule URLs found"
```

### 技術細節

1. **直接配置 submodule URL**：在 `git submodule update` 之前，直接修改每個 submodule 的 URL 配置
2. **使用正確的認證格式**：使用 `gitlab-ci-token:$TOKEN` 格式而不是 `oauth2:$TOKEN`
3. **添加調試輸出**：顯示更新後的 submodule URL 配置以便排查問題

## 預期效果

1. **解決認證問題**：GitLab submodule 能夠正確使用 token 進行認證
2. **提高部署成功率**：避免因 submodule 克隆失敗導致的部署中斷
3. **增強可維護性**：添加更多調試輸出，便於未來問題排查

## 測試驗證

部署完成後，應該能看到：
- 所有 submodule 成功克隆
- 子模塊目錄包含正確的文件
- 部署流程順利進行到下一步

## 風險評估

- **低風險**：只是修改認證配置，不影響核心功能
- **向後兼容**：不會影響現有的部署流程
- **可回滾**：如有問題可以快速回滾到之前的配置

## 相關文件

- `cloudbuild.yaml` - 主要修改文件
- GitLab submodules:
  - `app/Jobs/qtm-cointerpretation`
  - `app/Jobs/qtm-analysis` 
  - `app/Jobs/hrv` 