# PRD - 部署診斷增強

**文檔創建時間**: 2025-06-16 23:20:00  
**需求類型**: 系統增強  
**優先級**: 高  

## 問題描述

在 Cloud Build 部署過程中，第 11 步「等待部署就緒並切換服務」出現超時失敗：

```
Step #11 - "Wait for Deployment and Switch Service": ❌ 部署失敗: 超時：新部署未能在 5 分鐘內就緒
```

### 具體症狀
- Deployment 狀態一直為空
- Replicas 始終為 0/1
- 新的 Deployment `app-v20250616230416` 無法在 5 分鐘內就緒
- 缺乏詳細的診斷信息來定位問題根因

## 需求分析

### 主要需求
1. **增強診斷能力**: 在部署失敗時提供詳細的診斷信息
2. **改善監控**: 在等待過程中提供更多狀態信息
3. **快速診斷工具**: 提供獨立的診斷腳本供手動檢查

### 用戶場景
- **場景 1**: 開發人員需要快速了解部署失敗的原因
- **場景 2**: 運維人員需要在部署過程中監控詳細狀態
- **場景 3**: 故障排除時需要全面的系統狀態檢查

## 解決方案

### 1. 增強 Cloud Build 診斷功能

#### 1.1 錯誤處理函數增強
在 `cloudbuild.yaml` 第 11 步中增加詳細診斷功能：

```bash
deployment_error() {
    # 執行詳細診斷
    echo "🔍 開始詳細診斷..."
    
    # 檢查 Deployment 詳細狀態
    kubectl describe deployment "$dep_next"
    
    # 檢查 ReplicaSet 狀態
    kubectl get rs -l "app=$dep_next" -o wide
    kubectl describe rs -l "app=$dep_next"
    
    # 檢查 Pod 狀態和日誌
    kubectl get pods -l "version=${_VERSION_NEXT}" -o wide
    # ... 更多診斷邏輯
}
```

#### 1.2 等待邏輯增強
- 增加 Deployment 存在性檢查
- 提供更詳細的狀態信息（Available、Progressing、Updated Replicas）
- 每 30 秒顯示詳細診斷信息
- 顯示問題 Pod 的詳細狀態

### 2. 診斷工具開發

#### 2.1 詳細診斷腳本 (`tools/k8s__show_status`)
功能特性：
- 全面的 Kubernetes 資源狀態檢查
- 彩色輸出和結構化信息展示
- 包含 9 個診斷階段：
  1. Deployment 狀態檢查
  2. ReplicaSet 狀態檢查
  3. Pod 狀態和日誌檢查
  4. Service 狀態檢查
  5. ConfigMap 狀態檢查
  6. 節點資源檢查
  7. 事件檢查
  8. HPA 狀態檢查
  9. 診斷建議提供

使用方式：
```bash
./tools/k8s__show_status <deployment-name> [namespace]
```

#### 2.2 快速診斷腳本 (`tools/k8s_quick_diagnose.sh`)
功能特性：
- 快速概覽所有相關資源
- 自動識別最新 Deployment
- 重點檢查問題 Pod
- 提供快速診斷建議

使用方式：
```bash
./tools/k8s_quick_diagnose.sh [deployment-pattern] [namespace]
```

## 技術實現

### 修改的文件
1. **`cloudbuild.yaml`**
   - 增強第 11 步的錯誤處理函數
   - 改善等待邏輯的狀態監控
   - 添加詳細的診斷信息輸出

2. **`tools/k8s__show_status`**
   - 完全重寫為專業的診斷工具
   - 支持彩色輸出和結構化信息
   - 提供全面的系統狀態檢查

3. **`tools/k8s_quick_diagnose.sh`**
   - 新建快速診斷腳本
   - 專注於常見問題的快速檢查

### 關鍵改進點

#### 1. 診斷信息增強
- **之前**: 只顯示基本的 Deployment 狀態
- **現在**: 顯示 Available、Progressing、Ready/Desired/Updated Replicas

#### 2. 錯誤處理增強
- **之前**: 簡單的超時錯誤信息
- **現在**: 詳細的系統狀態診斷，包括：
  - Deployment 和 ReplicaSet 詳細狀態
  - Pod 狀態和日誌
  - 系統事件
  - 節點資源狀況
  - ConfigMap 狀態

#### 3. 監控頻率優化
- **之前**: 每 10 秒簡單狀態檢查
- **現在**: 每 10 秒基本狀態 + 每 30 秒詳細診斷

## 預期效果

### 1. 問題定位效率提升
- 從「不知道為什麼失敗」到「清楚知道失敗原因」
- 減少故障排除時間 70%

### 2. 運維體驗改善
- 實時監控部署進度
- 提前發現潛在問題
- 自動化診斷建議

### 3. 系統可靠性提升
- 更好的錯誤處理機制
- 詳細的日誌記錄
- 標準化的診斷流程

## 使用指南

### 部署失敗時的診斷流程

1. **查看 Cloud Build 日誌**
   - 檢查增強的診斷信息
   - 注意錯誤處理函數的輸出

2. **使用快速診斷工具**
   ```bash
   ./tools/k8s_quick_diagnose.sh
   ```

3. **使用詳細診斷工具**
   ```bash
   ./tools/k8s__show_status app-v20250616230416
   ```

4. **根據診斷結果採取行動**
   - Pod Pending: 檢查節點資源
   - CrashLoopBackOff: 檢查應用程式配置
   - ImagePullBackOff: 檢查映像權限
   - ConfigError: 檢查 ConfigMap

## 常見問題解決方案

### 1. Pod 無法啟動 (Pending)
**可能原因**:
- 節點資源不足
- 調度約束問題
- 存儲卷問題

**診斷命令**:
```bash
kubectl describe pod <pod-name>
kubectl top nodes
```

### 2. 容器崩潰 (CrashLoopBackOff)
**可能原因**:
- 應用程式配置錯誤
- 環境變數問題
- 依賴服務不可用

**診斷命令**:
```bash
kubectl logs <pod-name> --previous
kubectl describe pod <pod-name>
```

### 3. 映像拉取失敗 (ImagePullBackOff)
**可能原因**:
- 映像名稱或標籤錯誤
- 映像倉庫權限問題
- 網路連接問題

**診斷命令**:
```bash
kubectl describe pod <pod-name>
# 檢查映像名稱和權限
```

## 後續改進計劃

1. **自動化修復建議**: 基於診斷結果提供自動修復腳本
2. **監控整合**: 將診斷信息整合到監控系統
3. **通知機制**: 部署失敗時自動發送詳細診斷報告
4. **歷史分析**: 收集診斷數據進行趨勢分析

## 結論

通過這次增強，我們顯著提升了部署系統的診斷能力和運維體驗。當部署失敗時，系統現在能夠：

1. **自動提供詳細診斷信息**
2. **指導用戶快速定位問題**
3. **提供標準化的故障排除流程**

這將大大減少部署問題的解決時間，提升整體系統的可靠性和可維護性。 