# PRD-2025-06-20 16:04:28-修復部署腳本jq解析錯誤

## 問題描述

在執行部署腳本 `./deploy-eu qtm-api fra service/v1.43.0 bot` 時遇到以下錯誤：

```
parse error: Invalid string: control characters from U+0000 through U+001F must be escaped at line 4, column 26
```

## 問題分析

錯誤發生在 `retrieve_service_version_inner` 函數中，當嘗試使用 `jq` 解析從 API 端點 (`https://fra-qtm-api.qtmedical.com/api/version`) 返回的 JSON 響應時。

問題原因：
1. API 返回的 JSON 中包含了未轉義的控制字符（U+0000 到 U+001F）
2. `jq` 無法解析包含這些控制字符的 JSON 字符串

## 解決方案

### 1. 清理 API 響應中的控制字符

在 `tools/deploy-eu` 的 `retrieve_service_version_inner` 函數中，使用 `tr` 命令移除所有控制字符：

```bash
# 原始代碼
current_version=$(curl -s https://${host}/api/version)
echo "$current_version" | jq '.'

# 修改後的代碼
current_version=$(curl -s https://${host}/api/version)
# Clean control characters from the response before parsing with jq
cleaned_version=$(echo "$current_version" | tr -d '\000-\037')
echo "$cleaned_version" | jq '.' || echo "Failed to parse version response: $current_version"
```

### 2. 更新版本比較邏輯

使用清理後的版本進行比較：

```bash
# 原始代碼
if [ "$(echo "$current_version" | jq -r .commit)" == "$next_sha" ]; then

# 修改後的代碼
if [ "$(echo "$cleaned_version" | jq -r .commit 2>/dev/null)" == "$next_sha" ]; then
```

### 3. 修復 git describe 多行輸出問題

在 `repo__gitinfo_inner` 函數中，確保 `git describe` 只返回單行：

```bash
# 原始代碼
repo_desc=$(git show -s --format=%ci $(git describe --always))

# 修改後的代碼（第一次嘗試）
repo_desc=$(git show -s --format=%ci $(git describe --always | head -n 1))

# 最終修改（簡化方案）
# Simplify to just get the commit date of HEAD
repo_desc=$(git show -s --format=%ci HEAD)
```

### 4. 修復 "fatal: Needed a single revision" 錯誤

問題原因：當 `git describe --always` 返回包含換行符的標籤資訊時（例如帶有 Tagger 資訊的 annotated tag），會導致 `git show` 命令失敗。

解決方案：直接使用 `HEAD` 作為參考，避免複雜的命令替換：

```bash
# 簡化為直接獲取 HEAD commit 的日期
repo_desc=$(git show -s --format=%ci HEAD)
```

### 5. 處理 API 不可用的情況（502 錯誤）

問題描述：當 API 端點返回 502 錯誤（HTML 格式）時，部署腳本會因為無法解析 HTML 而中斷。

解決方案：將版本檢查改為非關鍵步驟，即使失敗也繼續部署：

```bash
# 檢查響應是否為 HTML 錯誤頁面或空值
if [[ "$current_version" =~ ^[[:space:]]*\<html || -z "$current_version" ]]; then
    echo "⚠️  Warning: Unable to fetch current version (API returned error or is unavailable)"
    echo "Continuing with deployment..."
else
    # 嘗試解析 JSON
    cleaned_version=$(echo "$current_version" | tr -d '\000-\037')
    if echo "$cleaned_version" | jq '.' >/dev/null 2>&1; then
        echo "$cleaned_version" | jq '.'
    else
        echo "⚠️  Warning: Unable to parse version response"
        echo "Raw response: $current_version"
        echo "Continuing with deployment..."
    fi
fi
```

主要改進：
- 檢測 HTML 錯誤響應（502、503 等）
- 所有錯誤都只顯示警告，不中斷部署
- 添加更多錯誤處理和友好的錯誤訊息
- 函數最後總是返回 0（成功）以確保部署繼續

## 實施細節

1. 使用 `tr -d '\000-\037'` 移除所有 ASCII 控制字符（0-31）
2. 添加錯誤處理，如果 `jq` 解析失敗會顯示原始響應
3. 在版本比較時使用 `2>/dev/null` 抑制潛在的錯誤消息
4. 簡化 git 命令，避免複雜的命令替換和多行輸出問題
5. 將版本檢查改為非關鍵步驟，確保部署流程的穩定性

## 測試驗證

修改完成後，重新執行部署腳本應該能正常工作：

```bash
./deploy-eu qtm-api fra service/v1.43.0 bot
```

## 影響範圍

- 影響檔案：`tools/deploy-eu`
- 影響函數：
  - `retrieve_service_version_inner`
  - `repo__gitinfo_inner`
- 影響環境：所有使用此部署腳本的環境（dev, sta, pro, aus, fra）

## 後續建議

1. 考慮在 API 端修復返回的 JSON 格式，確保不包含控制字符
2. 為部署腳本添加更多的錯誤處理和日誌記錄
3. 考慮將 JSON 處理邏輯抽取為獨立的函數，便於重用和測試 