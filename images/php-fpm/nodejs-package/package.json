{"name": "ecgview", "description": "Generating ECG views from ECG data", "version": "0.2.0", "author": "<PERSON>", "bin": {"ecgview": "./bin/run"}, "bugs": "http://gitlab.intra.qtmedical.com/WebService/ecgview/issues", "dependencies": {"@oclif/command": "^1.5.0", "@oclif/config": "^1.7.4", "@oclif/plugin-help": "^2.1.1", "chalk": "^2.4.1", "cli-ux": "^4.8.1", "d3-node": "^2.0.1", "d3node-output": "^1.0.2"}, "devDependencies": {"@oclif/test": "^1.2.0", "chai": "^4.1.2", "eslint": "^5.5.0", "eslint-config-oclif": "^3.0.0", "mocha": "^5.1.1", "nyc": "^12.0.2"}, "engines": {"node": ">=8.0.0"}, "files": ["/bin", "/src"], "homepage": "http://gitlab.intra.qtmedical.com/WebService/ecgview", "keywords": ["oclif"], "license": "Proprietary", "main": "src/index.js", "oclif": {"bin": "ecgview"}, "repository": "http://gitlab.intra.qtmedical.com/WebService/ecgview.git", "scripts": {"posttest": "eslint .", "test": "nyc mocha --forbid-only \"test/**/*.test.js\""}}