{"name": "ecgview", "version": "0.2.0", "lockfileVersion": 1, "requires": true, "dependencies": {"@babel/code-frame": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0.tgz", "integrity": "sha512-OfC2uemaknXr87bdLUkWog7nYuliM9Ij5HUcajsVcMCpQrcLmtxRbVFTIqmcSkSeYRBFBRxs2FiUqFJDLdiebA==", "dev": true, "requires": {"@babel/highlight": "^7.0.0"}}, "@babel/generator": {"version": "7.0.0-beta.51", "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.51.tgz", "integrity": "sha1-bHV1/952HQdIXgS67cA5LG2eMPY=", "dev": true, "requires": {"@babel/types": "7.0.0-beta.51", "jsesc": "^2.5.1", "lodash": "^4.17.5", "source-map": "^0.5.0", "trim-right": "^1.0.1"}}, "@babel/helper-function-name": {"version": "7.0.0-beta.51", "resolved": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.0.0-beta.51.tgz", "integrity": "sha1-IbSHSiJ8+Z7K/MMKkDAtpaJkBWE=", "dev": true, "requires": {"@babel/helper-get-function-arity": "7.0.0-beta.51", "@babel/template": "7.0.0-beta.51", "@babel/types": "7.0.0-beta.51"}}, "@babel/helper-get-function-arity": {"version": "7.0.0-beta.51", "resolved": "https://registry.npmjs.org/@babel/helper-get-function-arity/-/helper-get-function-arity-7.0.0-beta.51.tgz", "integrity": "sha1-MoGy0EWvlcFyzpGyCCXYXqRnZBE=", "dev": true, "requires": {"@babel/types": "7.0.0-beta.51"}}, "@babel/helper-split-export-declaration": {"version": "7.0.0-beta.51", "resolved": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.0.0-beta.51.tgz", "integrity": "sha1-imw/ZsTSZTUvwHdIT59ugKUauXg=", "dev": true, "requires": {"@babel/types": "7.0.0-beta.51"}}, "@babel/highlight": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.0.0.tgz", "integrity": "sha512-UFMC4ZeFC48Tpvj7C8UgLvtkaUuovQX+5xNWrsIoMG8o2z+XFKjKaN9iVmS84dPwVN00W4wPmqvYoZF3EGAsfw==", "dev": true, "requires": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.0.0-beta.51", "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-beta.51.tgz", "integrity": "sha1-J87C30Cd9gr1gnDtj2qlVAnqhvY=", "dev": true}, "@babel/template": {"version": "7.0.0-beta.51", "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.0.0-beta.51.tgz", "integrity": "sha1-lgKkCuvPNXrpZ34lMu9fyBD1+/8=", "dev": true, "requires": {"@babel/code-frame": "7.0.0-beta.51", "@babel/parser": "7.0.0-beta.51", "@babel/types": "7.0.0-beta.51", "lodash": "^4.17.5"}, "dependencies": {"@babel/code-frame": {"version": "7.0.0-beta.51", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.51.tgz", "integrity": "sha1-vXHZsZKvl435FYKdOdQJRFZDmgw=", "dev": true, "requires": {"@babel/highlight": "7.0.0-beta.51"}}, "@babel/highlight": {"version": "7.0.0-beta.51", "resolved": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.0.0-beta.51.tgz", "integrity": "sha1-6IRK4loVlcz9QriWI7Q3bKBtIl0=", "dev": true, "requires": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}}, "js-tokens": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-3.0.2.tgz", "integrity": "sha1-mGbfOVECEw449/mWvOtlRDIJwls=", "dev": true}}}, "@babel/traverse": {"version": "7.0.0-beta.51", "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.0.0-beta.51.tgz", "integrity": "sha1-mB2vLOw0emIx06odnhgDsDqqpKg=", "dev": true, "requires": {"@babel/code-frame": "7.0.0-beta.51", "@babel/generator": "7.0.0-beta.51", "@babel/helper-function-name": "7.0.0-beta.51", "@babel/helper-split-export-declaration": "7.0.0-beta.51", "@babel/parser": "7.0.0-beta.51", "@babel/types": "7.0.0-beta.51", "debug": "^3.1.0", "globals": "^11.1.0", "invariant": "^2.2.0", "lodash": "^4.17.5"}, "dependencies": {"@babel/code-frame": {"version": "7.0.0-beta.51", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.51.tgz", "integrity": "sha1-vXHZsZKvl435FYKdOdQJRFZDmgw=", "dev": true, "requires": {"@babel/highlight": "7.0.0-beta.51"}}, "@babel/highlight": {"version": "7.0.0-beta.51", "resolved": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.0.0-beta.51.tgz", "integrity": "sha1-6IRK4loVlcz9QriWI7Q3bKBtIl0=", "dev": true, "requires": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}}, "js-tokens": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-3.0.2.tgz", "integrity": "sha1-mGbfOVECEw449/mWvOtlRDIJwls=", "dev": true}}}, "@babel/types": {"version": "7.0.0-beta.51", "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.51.tgz", "integrity": "sha1-2AK3tUO1g2x3iqaReXq/APPZfqk=", "dev": true, "requires": {"esutils": "^2.0.2", "lodash": "^4.17.5", "to-fast-properties": "^2.0.0"}}, "@oclif/command": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@oclif/command/-/command-1.5.0.tgz", "integrity": "sha512-ZKvLLLCtIVNgsUXIECOz7WhTOw5j+eyG5Ly7AncJrCN2ENfFfp5BobX6Vvg3P2eL01RxZhnSXIiyJX79QLiD6Q==", "requires": {"@oclif/errors": "^1.1.2", "@oclif/parser": "^3.6.0", "debug": "^3.1.0", "semver": "^5.5.0"}}, "@oclif/config": {"version": "1.7.4", "resolved": "https://registry.npmjs.org/@oclif/config/-/config-1.7.4.tgz", "integrity": "sha512-vkK/9PXNsOEEdjsb7D/slclLJNvwTax7e2ApjOpnh8sYL34LcpVC9RA05ypCeoDvQiihN8ZdbMut0/AwskKAaQ==", "requires": {"debug": "^3.1.0", "tslib": "^1.9.3"}}, "@oclif/errors": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@oclif/errors/-/errors-1.2.0.tgz", "integrity": "sha512-DIcWydwfESHVMwrZt3lj5qLAiX296vdfA6K7utCa2/6nmT1JgBc102iFcjpmNxUzDKBU67NKCVBPSMCBDD/1wg==", "requires": {"clean-stack": "^1.3.0", "fs-extra": "^7.0.0", "indent-string": "^3.2.0", "strip-ansi": "^4.0.0", "wrap-ansi": "^3.0.1"}}, "@oclif/linewrap": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/@oclif/linewrap/-/linewrap-1.0.0.tgz", "integrity": "sha512-Ups2dShK52xXa8w6iBWLgcjPJWjais6KPJQq3gQ/88AY6BXoTX+MIGFPrWQO1KLMiQfoTpcLnUwloN4brrVUHw=="}, "@oclif/parser": {"version": "3.6.1", "resolved": "https://registry.npmjs.org/@oclif/parser/-/parser-3.6.1.tgz", "integrity": "sha512-H5gyGM3GaDFr1SHt7gsHfMEmt0/Q5SQYOqmtBlpofYaqiof8wdHZQAj4KY2oJpcy4tnsRJrFM3fN3GNEARBgtg==", "requires": {"@oclif/linewrap": "^1.0.0", "chalk": "^2.4.1", "tslib": "^1.9.3"}}, "@oclif/plugin-help": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/@oclif/plugin-help/-/plugin-help-2.1.1.tgz", "integrity": "sha512-eP1Z1yQNgQX3dpvCVQkr3iYVVVGKnWmI1pWxxqPIoUHZ6rmMZtYiawmPPpO/VSouV0ml0eoJ4HBPQfZfhiF8nw==", "requires": {"@oclif/command": "^1.5.0", "chalk": "^2.4.1", "indent-string": "^3.2.0", "lodash.template": "^4.4.0", "string-width": "^2.1.1", "widest-line": "^2.0.0", "wrap-ansi": "^4.0.0"}, "dependencies": {"wrap-ansi": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-4.0.0.tgz", "integrity": "sha512-uMTsj9rDb0/7kk1PbcbCcwvHUxp60fGDB/NNXpVa0Q+ic/e7y5+BwTxKfQ33VYgDppSwi/FBzpetYzo8s6tfbg==", "requires": {"ansi-styles": "^3.2.0", "string-width": "^2.1.1", "strip-ansi": "^4.0.0"}}}}, "@oclif/screen": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@oclif/screen/-/screen-1.0.2.tgz", "integrity": "sha512-9k2C/Oyk6OwcvyBrKbSGDfH0baI986Dn8ZDxl8viIg8shl40TSPVx+TqXExUeA0Pj02xSdXEt5YXgDFP5Opc5g=="}, "@oclif/test": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@oclif/test/-/test-1.2.0.tgz", "integrity": "sha512-AStDnb9Ku4U+5Zs/2UIsgLBFaAWTDujW4r3JJpbJhhu0jJjJjD3qVo4oJ3QOzuH4/17iPn3jd8L6Np1Q+1GP8A==", "dev": true, "requires": {"fancy-test": "^1.3.0"}}, "@types/chai": {"version": "4.1.4", "resolved": "https://registry.npmjs.org/@types/chai/-/chai-4.1.4.tgz", "integrity": "sha512-h6+VEw2Vr3ORiFCyyJmcho2zALnUq9cvdB/IO8Xs9itrJVCenC7o26A6+m7D0ihTTr65eS259H5/Ghl/VjYs6g==", "dev": true}, "@types/lodash": {"version": "4.14.116", "resolved": "https://registry.npmjs.org/@types/lodash/-/lodash-4.14.116.tgz", "integrity": "sha512-lRnAtKnxMXcYYXqOiotTmJd74uawNWuPnsnPrrO7HiFuE3npE2iQhfABatbYDyxTNqZNuXzcKGhw37R7RjBFLg==", "dev": true}, "@types/mocha": {"version": "5.2.5", "resolved": "https://registry.npmjs.org/@types/mocha/-/mocha-5.2.5.tgz", "integrity": "sha512-lAVp+Kj54ui/vLUFxsJTMtWvZraZxum3w3Nwkble2dNuV5VnPA+Mi2oGX9XYJAaIvZi3tn3cbjS/qcJXRb6Bww==", "dev": true}, "@types/nock": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@types/nock/-/nock-9.3.0.tgz", "integrity": "sha512-ZHf/X8rTQ5Tb1rHjxIJYqm55uO265agE3G7NoSXVa2ep+EcJXgB2fsme+zBvK7MhrxTwkC/xkB6THyv50u0MGw==", "dev": true, "requires": {"@types/node": "*"}}, "@types/node": {"version": "10.9.4", "resolved": "https://registry.npmjs.org/@types/node/-/node-10.9.4.tgz", "integrity": "sha512-fCHV45gS+m3hH17zgkgADUSi2RR1Vht6wOZ0jyHP8rjiQra9f+mIcgwPQHllmDocYOstIEbKlxbFDYlgrTPYqw==", "dev": true}, "@types/sinon": {"version": "5.0.2", "resolved": "https://registry.npmjs.org/@types/sinon/-/sinon-5.0.2.tgz", "integrity": "sha512-ifYuFq3GWyvRbqebGB4ZKLqezMGLXzhHv1Uefhg+uARYs/iO+v6Gu/BkpxTxsyM9NI++N/RCf5sWl3X9wBVLaw==", "dev": true}, "abab": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/abab/-/abab-1.0.4.tgz", "integrity": "sha1-X6rZwsB/YN12dw9xzwJbYqY8/U4="}, "acorn": {"version": "5.7.2", "resolved": "https://registry.npmjs.org/acorn/-/acorn-5.7.2.tgz", "integrity": "sha512-cJrKCNcr2kv8dlDnbw+JPUGjHZzo4myaxOLmpOX8a+rgX94YeTcTMv/LFJUSByRpc+i4GgVnnhLxvMu/2Y+rqw==", "dev": true}, "acorn-globals": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-3.1.0.tgz", "integrity": "sha1-/YJw9x+7SZawBPqIDuXUZXOnMb8=", "requires": {"acorn": "^4.0.4"}, "dependencies": {"acorn": {"version": "4.0.13", "resolved": "https://registry.npmjs.org/acorn/-/acorn-4.0.13.tgz", "integrity": "sha1-EFSVrlNh1pe9GVyCUZLhrX8lN4c="}}}, "acorn-jsx": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-4.1.1.tgz", "integrity": "sha512-JY+iV6r+cO21KtntVvFkD+iqjtdpRUpGqKWgfkCdZq1R+kbreEl8EcdcJR4SmiIgsIQT33s6QzheQ9a275Q8xw==", "dev": true, "requires": {"acorn": "^5.0.3"}}, "agent-base": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-4.2.1.tgz", "integrity": "sha512-JVwXMr9nHYTUXsBFKUqhJwvlcYU/blreOEUkhNR2eXZIvwd+c+o5V4MgDPKWnMS/56awN3TRzIP+KoPn+roQtg==", "requires": {"es6-promisify": "^5.0.0"}}, "ajv": {"version": "6.5.3", "resolved": "https://registry.npmjs.org/ajv/-/ajv-6.5.3.tgz", "integrity": "sha512-LqZ9wY+fx3UMiiPd741yB2pj3hhil+hQc8taf4o2QGRFpWgZ2V5C8HA165DY9sS3fJwsk7uT7ZlFEyC3Ig3lLg==", "dev": true, "requires": {"fast-deep-equal": "^2.0.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ajv-keywords": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.2.0.tgz", "integrity": "sha1-6GuBnGAs+IIa1jdBNpjx3sAhhHo=", "dev": true}, "ansi-escapes": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-3.1.0.tgz", "integrity": "sha512-UgAb8H9D41AQnu/PbWlCofQVcnV4Gs2bBJi9eZPxfU/hgglFh3SMDMENRIqdr7H6XFnXdoknctFByVsCOotTVw=="}, "ansi-regex": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg="}, "ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "requires": {"color-convert": "^1.9.0"}}, "ansicolors": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/ansicolors/-/ansicolors-0.3.2.tgz", "integrity": "sha1-ZlWX3oap/+Oqm/vmyuXG6kJrSXk="}, "argparse": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz", "integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==", "dev": true, "requires": {"sprintf-js": "~1.0.2"}}, "array-equal": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/array-equal/-/array-equal-1.0.0.tgz", "integrity": "sha1-jCpe8kcv2ep0KwTHenUJO6J1fJM="}, "array-union": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/array-union/-/array-union-1.0.2.tgz", "integrity": "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=", "dev": true, "requires": {"array-uniq": "^1.0.1"}}, "array-uniq": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/array-uniq/-/array-uniq-1.0.3.tgz", "integrity": "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=", "dev": true}, "arrify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/arrify/-/arrify-1.0.1.tgz", "integrity": "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=", "dev": true}, "asn1": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.2.4.tgz", "integrity": "sha512-jxwzQpLQjSmWXgwaCZE9Nz+glAG01yF1QnWgbhGwHI5A6FRIEY6IVqtHhIepHqI7/kyEyQEagBC5mBEFlIYvdg==", "requires": {"safer-buffer": "~2.1.0"}}, "assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="}, "assertion-error": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/assertion-error/-/assertion-error-1.1.0.tgz", "integrity": "sha512-jgsaNduz+ndvGyFt3uSuWqvy4lCnIJiovtouQN5JZHOKCS2QuhEdbcQHFhVksz2N2U9hXJo8odG7ETyWlEeuDw==", "dev": true}, "async-limiter": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/async-limiter/-/async-limiter-1.0.0.tgz", "integrity": "sha512-jp/uFnooOiO+L211eZOoSyzpOITMXx1rBITauYykG3BRYPu8h0UcxsPNB04RR5vo4Tyz3+ay17tR6JVf9qzYWg=="}, "asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k="}, "aws-sign2": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz", "integrity": "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg="}, "aws4": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/aws4/-/aws4-1.8.0.tgz", "integrity": "sha512-ReZxvNHIOv88FlT7rxcXIIC0fPt4KZqZbOlivyWtXLt8ESx84zd3kMC6iK5jVeS2qt+g7ftS7ye4fi06X5rtRQ=="}, "balanced-match": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c="}, "bcrypt-pbkdf": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=", "optional": true, "requires": {"tweetnacl": "^0.14.3"}}, "brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "browser-stdout": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/browser-stdout/-/browser-stdout-1.3.1.tgz", "integrity": "sha512-qhAVI1+Av2X7qelOfAIYwXONood6XlZE/fXaBSmW/T5SzLAmCgzi+eiWE7fUvbHaeNBQH13UftjpXxsfLkMpgw==", "dev": true}, "buffer-from": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.1.tgz", "integrity": "sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A=="}, "caller-path": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/caller-path/-/caller-path-0.1.0.tgz", "integrity": "sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8=", "dev": true, "requires": {"callsites": "^0.2.0"}}, "callsites": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/callsites/-/callsites-0.2.0.tgz", "integrity": "sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo=", "dev": true}, "cardinal": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/cardinal/-/cardinal-2.1.1.tgz", "integrity": "sha1-fMEFXYItISlU0HsIXeolHMe8VQU=", "requires": {"ansicolors": "~0.3.2", "redeyed": "~2.1.0"}}, "caseless": {"version": "0.12.0", "resolved": "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="}, "chai": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chai/-/chai-4.1.2.tgz", "integrity": "sha1-D2RYS6ZC8PKs4oBiefTwbKI61zw=", "dev": true, "requires": {"assertion-error": "^1.0.1", "check-error": "^1.0.1", "deep-eql": "^3.0.0", "get-func-name": "^2.0.0", "pathval": "^1.0.0", "type-detect": "^4.0.0"}}, "chalk": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.1.tgz", "integrity": "sha512-ObN6h1v2fTJSmUXoS3nMQ92LbDK9be4TV+6G+omQlGJFdcUX5heKi1LZ1YnRMIgwTLEj3E24bT6tYni50rlCfQ==", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "chardet": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz", "integrity": "sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==", "dev": true}, "check-error": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/check-error/-/check-error-1.0.2.tgz", "integrity": "sha1-V00xLt2Iu13YkS6Sht1sCu1KrII=", "dev": true}, "circular-json": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/circular-json/-/circular-json-0.3.3.tgz", "integrity": "sha512-UZK3NBx2Mca+b5LsG7bY183pHWt5Y1xts4P3Pz7ENTwGVnJOUWbRb3ocjvX7hx9tq/yTAdclXm9sZ38gNuem4A==", "dev": true}, "clean-regexp": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/clean-regexp/-/clean-regexp-1.0.0.tgz", "integrity": "sha1-jffHquUf02h06PjQW5GAvBGj/tc=", "dev": true, "requires": {"escape-string-regexp": "^1.0.5"}}, "clean-stack": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/clean-stack/-/clean-stack-1.3.0.tgz", "integrity": "sha1-noIVAa6XmYbEax1m0tQy2y/UrjE="}, "cli-cursor": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-2.1.0.tgz", "integrity": "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=", "dev": true, "requires": {"restore-cursor": "^2.0.0"}}, "cli-ux": {"version": "4.8.1", "resolved": "https://registry.npmjs.org/cli-ux/-/cli-ux-4.8.1.tgz", "integrity": "sha512-ehGXI54J7A4WJOa+fe0GDxcl6xmYLQmXDHptTtsWQDqWNXFOJQJzTHaJaFVOSo7e1f/kXtfvS1sPttQqTw44BA==", "requires": {"@oclif/errors": "^1.2.0", "@oclif/linewrap": "^1.0.0", "@oclif/screen": "^1.0.2", "ansi-styles": "^3.2.1", "cardinal": "^2.1.1", "chalk": "^2.4.1", "clean-stack": "^1.3.0", "extract-stack": "^1.0.0", "fs-extra": "^7.0.0", "hyperlinker": "^1.0.0", "indent-string": "^3.2.0", "is-wsl": "^1.1.0", "lodash": "^4.17.10", "password-prompt": "^1.0.7", "semver": "^5.5.1", "strip-ansi": "^4.0.0", "supports-color": "^5.5.0", "supports-hyperlinks": "^1.0.1"}}, "cli-width": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/cli-width/-/cli-width-2.2.0.tgz", "integrity": "sha1-/xnt6Kml5XkyQUewwR8PvLq+1jk=", "dev": true}, "co": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="}, "color-convert": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}, "combined-stream": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.6.tgz", "integrity": "sha1-cj599ugBrFYTETp+RFqbactjKBg=", "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "2.11.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.11.0.tgz", "integrity": "sha512-b0553uYA5YAEGgyYIGYROzKQ7X5RAqedkfjiZxwi0kL1g3bOaBNNZfYkzt/CL0umgD5wc9Jec2FbB98CjkMRvQ=="}, "concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "concat-stream": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==", "requires": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "content-type-parser": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/content-type-parser/-/content-type-parser-1.0.2.tgz", "integrity": "sha512-lM4l4CnMEwOLHAHr/P6MEZwZFPJFtAAKgL6pogbXmVZggIqXhdB6RbBtPOTsw2FcXwYhehRGERJmRrjOiIB8pQ=="}, "core-util-is": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="}, "cross-spawn": {"version": "6.0.5", "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.5.tgz", "integrity": "sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==", "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "cssom": {"version": "0.3.4", "resolved": "https://registry.npmjs.org/cssom/-/cssom-0.3.4.tgz", "integrity": "sha512-+7prCSORpXNeR4/fUP3rL+TzqtiFfhMvTd7uEqMdgPvLPt4+uzFUeufx5RHjGTACCargg/DiEt/moMQmvnfkog=="}, "cssstyle": {"version": "0.2.37", "resolved": "https://registry.npmjs.org/cssstyle/-/cssstyle-0.2.37.tgz", "integrity": "sha1-VBCXI0yyUTyDzu06zdwn/yeYfVQ=", "requires": {"cssom": "0.3.x"}}, "d3": {"version": "5.4.0", "resolved": "https://registry.npmjs.org/d3/-/d3-5.4.0.tgz", "integrity": "sha1-CQGZqFadHeI9BKP/B/4TYJX+p04=", "requires": {"d3-array": "1", "d3-axis": "1", "d3-brush": "1", "d3-chord": "1", "d3-collection": "1", "d3-color": "1", "d3-contour": "1", "d3-dispatch": "1", "d3-drag": "1", "d3-dsv": "1", "d3-ease": "1", "d3-fetch": "1", "d3-force": "1", "d3-format": "1", "d3-geo": "1", "d3-hierarchy": "1", "d3-interpolate": "1", "d3-path": "1", "d3-polygon": "1", "d3-quadtree": "1", "d3-random": "1", "d3-scale": "2", "d3-scale-chromatic": "1", "d3-selection": "1", "d3-shape": "1", "d3-time": "1", "d3-time-format": "2", "d3-timer": "1", "d3-transition": "1", "d3-voronoi": "1", "d3-zoom": "1"}}, "d3-array": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/d3-array/-/d3-array-1.2.4.tgz", "integrity": "sha512-KHW6M86R+FUPYGb3R5XiYjXPq7VzwxZ22buHhAEVG5ztoEcZZMLov530mmccaqA1GghZArjQV46fuc8kUqhhHw=="}, "d3-axis": {"version": "1.0.12", "resolved": "https://registry.npmjs.org/d3-axis/-/d3-axis-1.0.12.tgz", "integrity": "sha512-ejINPfPSNdGFKEOAtnBtdkpr24c4d4jsei6Lg98mxf424ivoDP2956/5HDpIAtmHo85lqT4pruy+zEgvRUBqaQ=="}, "d3-brush": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/d3-brush/-/d3-brush-1.0.6.tgz", "integrity": "sha512-lGSiF5SoSqO5/mYGD5FAeGKKS62JdA1EV7HPrU2b5rTX4qEJJtpjaGLJngjnkewQy7UnGstnFd3168wpf5z76w==", "requires": {"d3-dispatch": "1", "d3-drag": "1", "d3-interpolate": "1", "d3-selection": "1", "d3-transition": "1"}}, "d3-chord": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/d3-chord/-/d3-chord-1.0.6.tgz", "integrity": "sha512-JXA2Dro1Fxw9rJe33Uv+Ckr5IrAa74TlfDEhE/jfLOaXegMQFQTAgAw9WnZL8+HxVBRXaRGCkrNU7pJeylRIuA==", "requires": {"d3-array": "1", "d3-path": "1"}}, "d3-collection": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/d3-collection/-/d3-collection-1.0.7.tgz", "integrity": "sha512-ii0/r5f4sjKNTfh84Di+DpztYwqKhEyUlKoPrzUFfeSkWxjW49xU2QzO9qrPrNkpdI0XJkfzvmTu8V2Zylln6A=="}, "d3-color": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/d3-color/-/d3-color-1.2.3.tgz", "integrity": "sha512-x37qq3ChOTLd26hnps36lexMRhNXEtVxZ4B25rL0DVdDsGQIJGB18S7y9XDwlDD6MD/ZBzITCf4JjGMM10TZkw=="}, "d3-contour": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/d3-contour/-/d3-contour-1.3.2.tgz", "integrity": "sha512-hoPp4K/rJCu0ladiH6zmJUEz6+u3lgR+GSm/QdM2BBvDraU39Vr7YdDCicJcxP1z8i9B/2dJLgDC1NcvlF8WCg==", "requires": {"d3-array": "^1.1.1"}}, "d3-dispatch": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/d3-dispatch/-/d3-dispatch-1.0.5.tgz", "integrity": "sha512-vwKx+lAqB1UuCeklr6Jh1bvC4SZgbSqbkGBLClItFBIYH4vqDJCA7qfoy14lXmJdnBOdxndAMxjCbImJYW7e6g=="}, "d3-drag": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/d3-drag/-/d3-drag-1.2.3.tgz", "integrity": "sha512-8S3HWCAg+ilzjJsNtWW1Mutl74Nmzhb9yU6igspilaJzeZVFktmY6oO9xOh5TDk+BM2KrNFjttZNoJJmDnkjkg==", "requires": {"d3-dispatch": "1", "d3-selection": "1"}}, "d3-dsv": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/d3-dsv/-/d3-dsv-1.0.10.tgz", "integrity": "sha512-vqklfpxmtO2ZER3fq/B33R/BIz3A1PV0FaZRuFM8w6jLo7sUX1BZDh73fPlr0s327rzq4H6EN1q9U+eCBCSN8g==", "requires": {"commander": "2", "iconv-lite": "0.4", "rw": "1"}}, "d3-ease": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/d3-ease/-/d3-ease-1.0.5.tgz", "integrity": "sha512-Ct1O//ly5y5lFM9YTdu+ygq7LleSgSE4oj7vUt9tPLHUi8VCV7QoizGpdWRWAwCO9LdYzIrQDg97+hGVdsSGPQ=="}, "d3-fetch": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/d3-fetch/-/d3-fetch-1.1.2.tgz", "integrity": "sha512-S2loaQCV/ZeyTyIF2oP8D1K9Z4QizUzW7cWeAOAS4U88qOt3Ucf6GsmgthuYSdyB2HyEm4CeGvkQxWsmInsIVA==", "requires": {"d3-dsv": "1"}}, "d3-force": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/d3-force/-/d3-force-1.1.2.tgz", "integrity": "sha512-p1vcHAUF1qH7yR+e8ip7Bs61AHjLeKkIn8Z2gzwU2lwEf2wkSpWdjXG0axudTHsVFnYGlMkFaEsVy2l8tAg1Gw==", "requires": {"d3-collection": "1", "d3-dispatch": "1", "d3-quadtree": "1", "d3-timer": "1"}}, "d3-format": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/d3-format/-/d3-format-1.3.2.tgz", "integrity": "sha512-Z18Dprj96ExragQ0DeGi+SYPQ7pPfRMtUXtsg/ChVIKNBCzjO8XYJvRTC1usblx52lqge56V5ect+frYTQc8WQ=="}, "d3-geo": {"version": "1.11.1", "resolved": "https://registry.npmjs.org/d3-geo/-/d3-geo-1.11.1.tgz", "integrity": "sha512-GsG7x9G9sykseLviOVSJ3h5yjw0ItLopOtuDQKUt1TRklEegCw5WAmnIpYYiCkSH/QgUMleAeE2xZK38Qb+1+Q==", "requires": {"d3-array": "1"}}, "d3-hierarchy": {"version": "1.1.8", "resolved": "https://registry.npmjs.org/d3-hierarchy/-/d3-hierarchy-1.1.8.tgz", "integrity": "sha512-L+GHMSZNwTpiq4rt9GEsNcpLa4M96lXMR8M/nMG9p5hBE0jy6C+3hWtyZMenPQdwla249iJy7Nx0uKt3n+u9+w=="}, "d3-interpolate": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/d3-interpolate/-/d3-interpolate-1.3.2.tgz", "integrity": "sha512-NlNKGopqaz9qM1PXh9gBF1KSCVh+jSFErrSlD/4hybwoNX/gt1d8CDbDW+3i+5UOHhjC6s6nMvRxcuoMVNgL2w==", "requires": {"d3-color": "1"}}, "d3-node": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/d3-node/-/d3-node-2.0.1.tgz", "integrity": "sha512-uSnoThEAOGiA+SGzbWOGXX6C0iLN7wGHHHJwCV+BEG+9FrBIHbb9cwGa9f2IFREZEtdcnpTmz1ml7LLdUfyi/A==", "requires": {"d3": "5.4.0", "jsdom": "^9.12.0"}}, "d3-path": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/d3-path/-/d3-path-1.0.7.tgz", "integrity": "sha512-q0cW1RpvA5c5ma2rch62mX8AYaiLX0+bdaSM2wxSU9tXjU4DNvkx9qiUvjkuWCj3p22UO/hlPivujqMiR9PDzA=="}, "d3-polygon": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/d3-polygon/-/d3-polygon-1.0.5.tgz", "integrity": "sha512-RHhh1ZUJZfhgoqzWWuRhzQJvO7LavchhitSTHGu9oj6uuLFzYZVeBzaWTQ2qSO6bz2w55RMoOCf0MsLCDB6e0w=="}, "d3-quadtree": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/d3-quadtree/-/d3-quadtree-1.0.5.tgz", "integrity": "sha512-U2tjwDFbZ75JRAg8A+cqMvqPg1G3BE7UTJn3h8DHjY/pnsAfWdbJKgyfcy7zKjqGtLAmI0q8aDSeG1TVIKRaHQ=="}, "d3-random": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/d3-random/-/d3-random-1.1.2.tgz", "integrity": "sha512-6AK5BNpIFqP+cx/sreKzNjWbwZQCSUatxq+pPRmFIQaWuoD+NrbVWw7YWpHiXpCQ/NanKdtGDuB+VQcZDaEmYQ=="}, "d3-scale": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/d3-scale/-/d3-scale-2.1.2.tgz", "integrity": "sha512-bESpd64ylaKzCDzvULcmHKZTlzA/6DGSVwx7QSDj/EnX9cpSevsdiwdHFYI9ouo9tNBbV3v5xztHS2uFeOzh8Q==", "requires": {"d3-array": "^1.2.0", "d3-collection": "1", "d3-format": "1", "d3-interpolate": "1", "d3-time": "1", "d3-time-format": "2"}}, "d3-scale-chromatic": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/d3-scale-chromatic/-/d3-scale-chromatic-1.3.3.tgz", "integrity": "sha512-BWTipif1CimXcYfT02LKjAyItX5gKiwxuPRgr4xM58JwlLocWbjPLI7aMEjkcoOQXMkYsmNsvv3d2yl/OKuHHw==", "requires": {"d3-color": "1", "d3-interpolate": "1"}}, "d3-selection": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/d3-selection/-/d3-selection-1.3.2.tgz", "integrity": "sha512-OoXdv1nZ7h2aKMVg3kaUFbLLK5jXUFAMLD/Tu5JA96mjf8f2a9ZUESGY+C36t8R1WFeWk/e55hy54Ml2I62CRQ=="}, "d3-shape": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/d3-shape/-/d3-shape-1.2.2.tgz", "integrity": "sha512-hUGEozlKecFZ2bOSNt7ENex+4Tk9uc/m0TtTEHBvitCBxUNjhzm5hS2GrrVRD/ae4IylSmxGeqX5tWC2rASMlQ==", "requires": {"d3-path": "1"}}, "d3-time": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/d3-time/-/d3-time-1.0.10.tgz", "integrity": "sha512-hF+NTLCaJHF/JqHN5hE8HVGAXPStEq6/omumPE/SxyHVrR7/qQxusFDo0t0c/44+sCGHthC7yNGFZIEgju0P8g=="}, "d3-time-format": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/d3-time-format/-/d3-time-format-2.1.3.tgz", "integrity": "sha512-6k0a2rZryzGm5Ihx+aFMuO1GgelgIz+7HhB4PH4OEndD5q2zGn1mDfRdNrulspOfR6JXkb2sThhDK41CSK85QA==", "requires": {"d3-time": "1"}}, "d3-timer": {"version": "1.0.9", "resolved": "https://registry.npmjs.org/d3-timer/-/d3-timer-1.0.9.tgz", "integrity": "sha512-rT34J5HnQUHhcLvhSB9GjCkN0Ddd5Y8nCwDBG2u6wQEeYxT/Lf51fTFFkldeib/sE/J0clIe0pnCfs6g/lRbyg=="}, "d3-transition": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/d3-transition/-/d3-transition-1.1.3.tgz", "integrity": "sha512-tEvo3qOXL6pZ1EzcXxFcPNxC/Ygivu5NoBY6mbzidATAeML86da+JfVIUzon3dNM6UX6zjDx+xbYDmMVtTSjuA==", "requires": {"d3-color": "1", "d3-dispatch": "1", "d3-ease": "1", "d3-interpolate": "1", "d3-selection": "^1.1.0", "d3-timer": "1"}}, "d3-voronoi": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/d3-voronoi/-/d3-voronoi-1.1.4.tgz", "integrity": "sha512-dArJ32hchFsrQ8uMiTBLq256MpnZjeuBtdHpaDlYuQyjU0CVzCJl/BVW+SkszaAeH95D/8gxqAhgx0ouAWAfRg=="}, "d3-zoom": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/d3-zoom/-/d3-zoom-1.7.3.tgz", "integrity": "sha512-xEBSwFx5Z9T3/VrwDkMt+mr0HCzv7XjpGURJ8lWmIC8wxe32L39eWHIasEe/e7Ox8MPU4p1hvH8PKN2olLzIBg==", "requires": {"d3-dispatch": "1", "d3-drag": "1", "d3-interpolate": "1", "d3-selection": "1", "d3-transition": "1"}}, "d3node-output": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/d3node-output/-/d3node-output-1.0.2.tgz", "integrity": "sha512-EMv2mj3w7TY4jPzrzcATI7OdhQ/lnPicvHhN6m7Tddpet+a96V3zU14Jg8gUgKmoQ/6XhOsHMZQZhjibjh/6lQ==", "requires": {"puppeteer": "0.10.1"}}, "dashdash": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "requires": {"assert-plus": "^1.0.0"}}, "debug": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "requires": {"ms": "2.0.0"}}, "deep-eql": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/deep-eql/-/deep-eql-3.0.1.tgz", "integrity": "sha512-+QeIQyN5ZuO+3Uk5DYh6/1eKO0m0YmJFGNmFHGACpf1ClL1nmlV/p4gNgbl2pJGxgXb4faqo6UE+M5ACEMyVcw==", "dev": true, "requires": {"type-detect": "^4.0.0"}}, "deep-is": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.3.tgz", "integrity": "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ="}, "del": {"version": "2.2.2", "resolved": "https://registry.npmjs.org/del/-/del-2.2.2.tgz", "integrity": "sha1-wSyYHQZ4RshLyvhiz/kw2Qf/0ag=", "dev": true, "requires": {"globby": "^5.0.0", "is-path-cwd": "^1.0.0", "is-path-in-cwd": "^1.0.0", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "rimraf": "^2.2.8"}}, "delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="}, "diff": {"version": "3.5.0", "resolved": "https://registry.npmjs.org/diff/-/diff-3.5.0.tgz", "integrity": "sha512-A46qtFgd+g7pDZinpnwiRJtxbC1hpgf0uzP3iG89scHk0AUC7A1TGxf5OiiOUv/JMZR8GOt8hL900hV0bOy5xA==", "dev": true}, "doctrine": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz", "integrity": "sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==", "dev": true, "requires": {"esutils": "^2.0.2"}}, "ecc-jsbn": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=", "optional": true, "requires": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "es6-promise": {"version": "4.2.5", "resolved": "https://registry.npmjs.org/es6-promise/-/es6-promise-4.2.5.tgz", "integrity": "sha512-n6wvpdE43VFtJq+lUDYDBFUwV8TZbuGXLV4D6wKafg13ldznKsyEvatubnmUe31zcvelSzOHF+XbaT+Bl9ObDg=="}, "es6-promisify": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/es6-promisify/-/es6-promisify-5.0.0.tgz", "integrity": "sha1-UQnWLz5W6pZ8S2NQWu8IKRyKUgM=", "requires": {"es6-promise": "^4.0.3"}}, "escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "escodegen": {"version": "1.11.0", "resolved": "https://registry.npmjs.org/escodegen/-/escodegen-1.11.0.tgz", "integrity": "sha512-IeMV45ReixHS53K/OmfKAIztN/igDHzTJUhZM3k1jMhIZWjk45SMwAtBsEXiJp3vSPmTcu6CXn7mDvFHRN66fw==", "requires": {"esprima": "^3.1.3", "estraverse": "^4.2.0", "esutils": "^2.0.2", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "dependencies": {"esprima": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/esprima/-/esprima-3.1.3.tgz", "integrity": "sha1-/cpRzuYTOJXjyI1TXOSdv/YqRjM="}, "source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "optional": true}}}, "eslint": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/eslint/-/eslint-5.5.0.tgz", "integrity": "sha512-m+az4vYehIJgl1Z0gb25KnFXeqQRdNreYsei1jdvkd9bB+UNQD3fsuiC2AWSQ56P+/t++kFSINZXFbfai+krOw==", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "ajv": "^6.5.3", "chalk": "^2.1.0", "cross-spawn": "^6.0.5", "debug": "^3.1.0", "doctrine": "^2.1.0", "eslint-scope": "^4.0.0", "eslint-utils": "^1.3.1", "eslint-visitor-keys": "^1.0.0", "espree": "^4.0.0", "esquery": "^1.0.1", "esutils": "^2.0.2", "file-entry-cache": "^2.0.0", "functional-red-black-tree": "^1.0.1", "glob": "^7.1.2", "globals": "^11.7.0", "ignore": "^4.0.6", "imurmurhash": "^0.1.4", "inquirer": "^6.1.0", "is-resolvable": "^1.1.0", "js-yaml": "^3.12.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.3.0", "lodash": "^4.17.5", "minimatch": "^3.0.4", "mkdirp": "^0.5.1", "natural-compare": "^1.4.0", "optionator": "^0.8.2", "path-is-inside": "^1.0.2", "pluralize": "^7.0.0", "progress": "^2.0.0", "regexpp": "^2.0.0", "require-uncached": "^1.0.3", "semver": "^5.5.1", "strip-ansi": "^4.0.0", "strip-json-comments": "^2.0.1", "table": "^4.0.3", "text-table": "^0.2.0"}}, "eslint-ast-utils": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/eslint-ast-utils/-/eslint-ast-utils-1.1.0.tgz", "integrity": "sha512-otzzTim2/1+lVrlH19EfQQJEhVJSu0zOb9ygb3iapN6UlyaDtyRq4b5U1FuW0v1lRa9Fp/GJyHkSwm6NqABgCA==", "dev": true, "requires": {"lodash.get": "^4.4.2", "lodash.zip": "^4.2.0"}}, "eslint-config-oclif": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/eslint-config-oclif/-/eslint-config-oclif-3.0.0.tgz", "integrity": "sha512-MKNNWI6GNMZikYwSodS/oeOqengIMl4q3J0XfCYOareXpnMcJFgu3uun9Lfx2v2c7eXQHwWuTnbNCRXlfSAmng==", "dev": true, "requires": {"eslint-config-xo-space": "^0.20.0", "eslint-plugin-mocha": "^5.2.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-unicorn": "^5.0.0"}}, "eslint-config-xo": {"version": "0.24.2", "resolved": "https://registry.npmjs.org/eslint-config-xo/-/eslint-config-xo-0.24.2.tgz", "integrity": "sha512-ivQ7qISScW6gfBp+p31nQntz1rg34UCybd3uvlngcxt5Utsf4PMMi9QoAluLFcPUM5Tvqk4JGraR9qu3msKPKQ==", "dev": true}, "eslint-config-xo-space": {"version": "0.20.0", "resolved": "https://registry.npmjs.org/eslint-config-xo-space/-/eslint-config-xo-space-0.20.0.tgz", "integrity": "sha512-bOsoZA8M6v1HviDUIGVq1fLVnSu3mMZzn85m2tqKb73tSzu4GKD4Jd2Py4ZKjCgvCbRRByEB5HPC3fTMnnJ1uw==", "dev": true, "requires": {"eslint-config-xo": "^0.24.0"}}, "eslint-plugin-es": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/eslint-plugin-es/-/eslint-plugin-es-1.3.1.tgz", "integrity": "sha512-9XcVyZiQRVeFjqHw8qHNDAZcQLqaHlOGGpeYqzYh8S4JYCWTCO3yzyen8yVmA5PratfzTRWDwCOFphtDEG+w/w==", "dev": true, "requires": {"eslint-utils": "^1.3.0", "regexpp": "^2.0.0"}}, "eslint-plugin-mocha": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/eslint-plugin-mocha/-/eslint-plugin-mocha-5.2.0.tgz", "integrity": "sha512-4VTX/qIoxUFRnXLNm6bEhEJyfGnGagmQzV4TWXKzkZgIYyP2FSubEdCjEFTyS/dGwSVRWCWGX7jO7BK8R0kppg==", "dev": true, "requires": {"ramda": "^0.25.0"}}, "eslint-plugin-node": {"version": "7.0.1", "resolved": "https://registry.npmjs.org/eslint-plugin-node/-/eslint-plugin-node-7.0.1.tgz", "integrity": "sha512-lfVw3TEqThwq0j2Ba/Ckn2ABdwmL5dkOgAux1rvOk6CO7A6yGyPI2+zIxN6FyNkp1X1X/BSvKOceD6mBWSj4Yw==", "dev": true, "requires": {"eslint-plugin-es": "^1.3.1", "eslint-utils": "^1.3.1", "ignore": "^4.0.2", "minimatch": "^3.0.4", "resolve": "^1.8.1", "semver": "^5.5.0"}}, "eslint-plugin-unicorn": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/eslint-plugin-unicorn/-/eslint-plugin-unicorn-5.0.0.tgz", "integrity": "sha512-+a+BVAP/dcS3g8TqtQTOTH9ck7YjIR+czYL3USqZDGTVsFIfObQzHOcpbKVjLgXaTq8F4RS22ABC+B8sSFqLFw==", "dev": true, "requires": {"clean-regexp": "^1.0.0", "eslint-ast-utils": "^1.0.0", "import-modules": "^1.1.0", "lodash.camelcase": "^4.1.1", "lodash.kebabcase": "^4.0.1", "lodash.snakecase": "^4.0.1", "lodash.upperfirst": "^4.2.0", "safe-regex": "^1.1.0"}}, "eslint-scope": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-4.0.0.tgz", "integrity": "sha512-1G6UTDi7Jc1ELFwnR58HV4fK9OQK4S6N985f166xqXxpjU6plxFISJa2Ba9KCQuFa8RCnj/lSFJbHo7UFDBnUA==", "dev": true, "requires": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}}, "eslint-utils": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/eslint-utils/-/eslint-utils-1.3.1.tgz", "integrity": "sha512-Z7YjnIldX+2XMcjr7ZkgEsOj/bREONV60qYeB/bjMAqqqZ4zxKyWX+BOUkdmRmA9riiIPVvo5x86m5elviOk0Q==", "dev": true}, "eslint-visitor-keys": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.0.0.tgz", "integrity": "sha512-qzm/XxIbxm/FHyH341ZrbnMUpe+5Bocte9xkmFMzPMjRaZMcXww+MpBptFvtU+79L362nqiLhekCxCxDPaUMBQ==", "dev": true}, "espree": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/espree/-/espree-4.0.0.tgz", "integrity": "sha512-kapdTCt1bjmspxStVKX6huolXVV5ZfyZguY1lcfhVVZstce3bqxH9mcLzNn3/mlgW6wQ732+0fuG9v7h0ZQoKg==", "dev": true, "requires": {"acorn": "^5.6.0", "acorn-jsx": "^4.1.1"}}, "esprima": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "integrity": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="}, "esquery": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/esquery/-/esquery-1.0.1.tgz", "integrity": "sha512-SmiyZ5zIWH9VM+SRUReLS5Q8a7GxtRdxEBVZpm98rJM7Sb+A9DVCndXfkeFUd3byderg+EbDkfnevfCwynWaNA==", "dev": true, "requires": {"estraverse": "^4.0.0"}}, "esrecurse": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.2.1.tgz", "integrity": "sha512-64RBB++fIOAXPw3P9cy89qfMlvZEXZkqqJkjqqXIvzP5ezRZjW+lPWjw35UX/3EhUPFYbg5ER4JYgDw4007/DQ==", "dev": true, "requires": {"estraverse": "^4.1.0"}}, "estraverse": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-4.2.0.tgz", "integrity": "sha1-De4/7TH81GlhjOc0IJn8GvoL2xM="}, "esutils": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz", "integrity": "sha1-Cr9PHKpbyx96nYrMbepPqqBLrJs="}, "extend": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="}, "external-editor": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/external-editor/-/external-editor-3.0.3.tgz", "integrity": "sha512-bn71H9+qWoOQKyZDo25mOMVpSmXROAsTJVVVYzrrtol3d4y+AsKjf4Iwl2Q+IuT0kFSQ1qo166UuIwqYq7mGnA==", "dev": true, "requires": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}}, "extract-stack": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/extract-stack/-/extract-stack-1.0.0.tgz", "integrity": "sha1-uXrK+UQe6iMyUpYktzL8WhyBZfo="}, "extract-zip": {"version": "1.6.7", "resolved": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.6.7.tgz", "integrity": "sha1-qEC0uK9kAyZMjbV/Txp0Mz74H+k=", "requires": {"concat-stream": "1.6.2", "debug": "2.6.9", "mkdirp": "0.5.1", "yauzl": "2.4.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}}}, "extsprintf": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="}, "fancy-test": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/fancy-test/-/fancy-test-1.4.0.tgz", "integrity": "sha512-35CJcYGL/Y2K68EDf9TAMORgOUX2+EvzopPtHnFFQt4qXqzqV2fUXr5ZH4/ZPBPDrUfinLiWPxGjrHUiALlbqQ==", "dev": true, "requires": {"@types/chai": "^4.1.4", "@types/lodash": "^4.14.116", "@types/mocha": "^5.2.5", "@types/nock": "^9.3.0", "@types/node": "^10.9.4", "@types/sinon": "^5.0.2", "lodash": "^4.17.10", "mock-stdin": "^0.3.1", "stdout-stderr": "^0.1.9"}}, "fast-deep-equal": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz", "integrity": "sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=", "dev": true}, "fast-json-stable-stringify": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz", "integrity": "sha1-1RQsDK7msRifh9OnYREGT4bIu/I="}, "fast-levenshtein": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="}, "fd-slicer": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.0.1.tgz", "integrity": "sha1-i1vL2ewyfFBBv5qwI/1nUPEXfmU=", "requires": {"pend": "~1.2.0"}}, "figures": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/figures/-/figures-2.0.0.tgz", "integrity": "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=", "dev": true, "requires": {"escape-string-regexp": "^1.0.5"}}, "file-entry-cache": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-2.0.0.tgz", "integrity": "sha1-w5KZDD5oR4PYOLjISkXYoEhFg2E=", "dev": true, "requires": {"flat-cache": "^1.2.1", "object-assign": "^4.0.1"}}, "flat-cache": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/flat-cache/-/flat-cache-1.3.0.tgz", "integrity": "sha1-0wMLMrOBVPTjt+nHCfSQ9++XxIE=", "dev": true, "requires": {"circular-json": "^0.3.1", "del": "^2.0.2", "graceful-fs": "^4.1.2", "write": "^0.2.1"}}, "forever-agent": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="}, "form-data": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/form-data/-/form-data-2.3.2.tgz", "integrity": "sha1-SXBJi+YEwgwAXU9cI67NIda0kJk=", "requires": {"asynckit": "^0.4.0", "combined-stream": "1.0.6", "mime-types": "^2.1.12"}}, "fs-extra": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-7.0.0.tgz", "integrity": "sha512-EglNDLRpmaTWiD/qraZn6HREAEAHJcJOmxNEYwq6xeMKnVMAy3GUcFB+wXt2C6k4CNvB/mP1y/U3dzvKKj5OtQ==", "requires": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "functional-red-black-tree": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz", "integrity": "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=", "dev": true}, "get-func-name": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/get-func-name/-/get-func-name-2.0.0.tgz", "integrity": "sha1-6td0q+5y4gQJQzoGY2YCPdaIekE=", "dev": true}, "getpass": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "requires": {"assert-plus": "^1.0.0"}}, "glob": {"version": "7.1.3", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.3.tgz", "integrity": "sha512-vcfuiIxogLV4DlGBHIUOwI0IbrJ8HWPc4MU7HzviGeNho/UJDfi6B5p3sHeWIQ0KGIU0Jpxi5ZHxemQfLkkAwQ==", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "globals": {"version": "11.7.0", "resolved": "https://registry.npmjs.org/globals/-/globals-11.7.0.tgz", "integrity": "sha512-K8BNSPySfeShBQXsahYB/AbbWruVOTyVpgoIDnl8odPpeSfP2J5QO2oLFFdl2j7GfDCtZj2bMKar2T49itTPCg==", "dev": true}, "globby": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/globby/-/globby-5.0.0.tgz", "integrity": "sha1-69hGZ8oNuzMLmbz8aOrCvFQ3Dg0=", "dev": true, "requires": {"array-union": "^1.0.1", "arrify": "^1.0.0", "glob": "^7.0.3", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "graceful-fs": {"version": "4.1.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg="}, "growl": {"version": "1.10.3", "resolved": "https://registry.npmjs.org/growl/-/growl-1.10.3.tgz", "integrity": "sha512-hKlsbA5Vu3xsh1Cg3J7jSmX/WaW6A5oBeqzM88oNbCRQFz+zUaXm6yxS4RVytp1scBoJzSYl4YAEOQIt6O8V1Q==", "dev": true}, "har-schema": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz", "integrity": "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI="}, "har-validator": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/har-validator/-/har-validator-5.1.0.tgz", "integrity": "sha512-+qnmNjI4OfH2ipQ9VQOw23bBd/ibtfbVdK2fYbY4acTDqKTW/YDp9McimZdDbG8iV9fZizUqQMD5xvriB146TA==", "requires": {"ajv": "^5.3.0", "har-schema": "^2.0.0"}, "dependencies": {"ajv": {"version": "5.5.2", "resolved": "https://registry.npmjs.org/ajv/-/ajv-5.5.2.tgz", "integrity": "sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU=", "requires": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.3.0"}}, "fast-deep-equal": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-1.1.0.tgz", "integrity": "sha1-wFNHeBfIa1HaqFPIHgWbcz0CNhQ="}, "json-schema-traverse": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.3.1.tgz", "integrity": "sha1-NJptRMU6Ud6JtAgFxdXlm0F9M0A="}}}, "has-flag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="}, "he": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/he/-/he-1.1.1.tgz", "integrity": "sha1-k0EP0hsAlzUVH4howvJx80J+I/0=", "dev": true}, "html-encoding-sniffer": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-1.0.2.tgz", "integrity": "sha512-71l<PERSON>ziiDnsuabfdYiUeWdCVyKuqwWi23L8YeIgV9jSSZHCtb6wB1BKWooH7L3tn4/FuZJMVWyNaIDr4RGmaSYw==", "requires": {"whatwg-encoding": "^1.0.1"}}, "http-signature": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz", "integrity": "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=", "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}}, "https-proxy-agent": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-2.2.1.tgz", "integrity": "sha512-HPCTS1LW51bcyMYbxUIOO4HEOlQ1/1qRaFWcyxvwaqUS9TY88aoEuHUY33kuAh1YhVVaDQhLZsnPd+XNARWZlQ==", "requires": {"agent-base": "^4.1.0", "debug": "^3.1.0"}}, "hyperlinker": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/hyperlinker/-/hyperlinker-1.0.0.tgz", "integrity": "sha512-Ty8UblRWFEcfSuIaajM34LdPXIhbs1ajEX/BBPv24J+enSVaEVY63xQ6lTO9VRYS5LAoghIG0IDJ+p+IPzKUQQ=="}, "iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ignore": {"version": "4.0.6", "resolved": "https://registry.npmjs.org/ignore/-/ignore-4.0.6.tgz", "integrity": "sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==", "dev": true}, "import-modules": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/import-modules/-/import-modules-1.1.0.tgz", "integrity": "sha1-dI23nFzEK7lwHvq0JPiU5yYA6dw=", "dev": true}, "imurmurhash": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true}, "indent-string": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/indent-string/-/indent-string-3.2.0.tgz", "integrity": "sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok="}, "inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}, "inquirer": {"version": "6.2.0", "resolved": "https://registry.npmjs.org/inquirer/-/inquirer-6.2.0.tgz", "integrity": "sha512-QIEQG4YyQ2UYZGDC4srMZ7BjHOmNk1lR2JQj5UknBapklm6WHA+VVH7N+sUdX3A7NeCfGF8o4X1S3Ao7nAcIeg==", "dev": true, "requires": {"ansi-escapes": "^3.0.0", "chalk": "^2.0.0", "cli-cursor": "^2.1.0", "cli-width": "^2.0.0", "external-editor": "^3.0.0", "figures": "^2.0.0", "lodash": "^4.17.10", "mute-stream": "0.0.7", "run-async": "^2.2.0", "rxjs": "^6.1.0", "string-width": "^2.1.0", "strip-ansi": "^4.0.0", "through": "^2.3.6"}}, "invariant": {"version": "2.2.4", "resolved": "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz", "integrity": "sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==", "dev": true, "requires": {"loose-envify": "^1.0.0"}}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="}, "is-path-cwd": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-1.0.0.tgz", "integrity": "sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0=", "dev": true}, "is-path-in-cwd": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-path-in-cwd/-/is-path-in-cwd-1.0.1.tgz", "integrity": "sha512-FjV1RTW48E7CWM7eE/J2NJvAEEVektecDBVBE5Hh3nM1Jd0kvhHtX68Pr3xsDf857xt3Y4AkwVULK1Vku62aaQ==", "dev": true, "requires": {"is-path-inside": "^1.0.0"}}, "is-path-inside": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-1.0.1.tgz", "integrity": "sha1-jvW33lBDej/cprToZe96pVy0gDY=", "dev": true, "requires": {"path-is-inside": "^1.0.1"}}, "is-promise": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-promise/-/is-promise-2.1.0.tgz", "integrity": "sha1-eaKp7OfwlugPNtKy87wWwf9L8/o=", "dev": true}, "is-resolvable": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-resolvable/-/is-resolvable-1.1.0.tgz", "integrity": "sha512-qgDYXFSR5WvEfuS5dMj6oTMEbrrSaM0CrFk2Yiq/gXnBvD9pMa2jGXxyhGLfvhZpuMZe18CJpFxAt3CRs42NMg==", "dev": true}, "is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="}, "is-wsl": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-wsl/-/is-wsl-1.1.0.tgz", "integrity": "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0="}, "isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "isexe": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="}, "isstream": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="}, "istanbul-lib-coverage": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.1.tgz", "integrity": "sha512-nPvSZsVlbG9aLhZYaC3Oi1gT/tpyo3Yt5fNyf6NmcKIayz4VV/txxJFFKAK/gU4dcNn8ehsanBbVHVl0+amOLA==", "dev": true}, "istanbul-lib-instrument": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-2.3.2.tgz", "integrity": "sha512-l7TD/VnBsIB2OJvSyxaLW/ab1+92dxZNH9wLH7uHPPioy3JZ8tnx2UXUdKmdkgmP2EFPzg64CToUP6dAS3U32Q==", "dev": true, "requires": {"@babel/generator": "7.0.0-beta.51", "@babel/parser": "7.0.0-beta.51", "@babel/template": "7.0.0-beta.51", "@babel/traverse": "7.0.0-beta.51", "@babel/types": "7.0.0-beta.51", "istanbul-lib-coverage": "^2.0.1", "semver": "^5.5.0"}}, "js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "dev": true}, "js-yaml": {"version": "3.12.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.12.0.tgz", "integrity": "sha512-PIt2cnwmPfL4hKNwqeiuz4bKfnzHTBv6HyVgjahA6mPLwPDzjDWrplJBMjHUFxku/N3FlmrbyPclad+I+4mJ3A==", "dev": true, "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM=", "optional": true}, "jsdom": {"version": "9.12.0", "resolved": "https://registry.npmjs.org/jsdom/-/jsdom-9.12.0.tgz", "integrity": "sha1-6MVG//ywbADUgzyoRBD+1/igl9Q=", "requires": {"abab": "^1.0.3", "acorn": "^4.0.4", "acorn-globals": "^3.1.0", "array-equal": "^1.0.0", "content-type-parser": "^1.0.1", "cssom": ">= 0.3.2 < 0.4.0", "cssstyle": ">= 0.2.37 < 0.3.0", "escodegen": "^1.6.1", "html-encoding-sniffer": "^1.0.1", "nwmatcher": ">= 1.3.9 < 2.0.0", "parse5": "^1.5.1", "request": "^2.79.0", "sax": "^1.2.1", "symbol-tree": "^3.2.1", "tough-cookie": "^2.3.2", "webidl-conversions": "^4.0.0", "whatwg-encoding": "^1.0.1", "whatwg-url": "^4.3.0", "xml-name-validator": "^2.0.1"}, "dependencies": {"acorn": {"version": "4.0.13", "resolved": "https://registry.npmjs.org/acorn/-/acorn-4.0.13.tgz", "integrity": "sha1-EFSVrlNh1pe9GVyCUZLhrX8lN4c="}}}, "jsesc": {"version": "2.5.1", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-2.5.1.tgz", "integrity": "sha1-5CGiqOINawgZ3yiQj3glJrlt0f4=", "dev": true}, "json-schema": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz", "integrity": "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM="}, "json-schema-traverse": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "dev": true}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true}, "json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="}, "jsonfile": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz", "integrity": "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=", "requires": {"graceful-fs": "^4.1.6"}}, "jsprim": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz", "integrity": "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=", "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "levn": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz", "integrity": "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=", "requires": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}}, "lodash": {"version": "4.17.10", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.10.tgz", "integrity": "sha512-UejweD1pDoXu+AD825lWwp4ZGtSwgnpZxb3JDViD7StjQz+Nb/6l093lx4OQ0foGWNRoc19mWy7BzL+UAK2iVg=="}, "lodash._reinterpolate": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash._reinterpolate/-/lodash._reinterpolate-3.0.0.tgz", "integrity": "sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0="}, "lodash.camelcase": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz", "integrity": "sha1-soqmKIorn8ZRA1x3EfZathkDMaY=", "dev": true}, "lodash.get": {"version": "4.4.2", "resolved": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.4.2.tgz", "integrity": "sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk=", "dev": true}, "lodash.kebabcase": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz", "integrity": "sha1-hImxyw0p/4gZXM7KRI/21swpXDY=", "dev": true}, "lodash.snakecase": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-4.1.1.tgz", "integrity": "sha1-OdcUo1NXFHg3rv1ktdy7Fr7Nj40=", "dev": true}, "lodash.template": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/lodash.template/-/lodash.template-4.4.0.tgz", "integrity": "sha1-5zoDhcg1VZF0bgILmWecaQ5o+6A=", "requires": {"lodash._reinterpolate": "~3.0.0", "lodash.templatesettings": "^4.0.0"}}, "lodash.templatesettings": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/lodash.templatesettings/-/lodash.templatesettings-4.1.0.tgz", "integrity": "sha1-K01OlbpEDZFf8IvImeRVNmZxMxY=", "requires": {"lodash._reinterpolate": "~3.0.0"}}, "lodash.upperfirst": {"version": "4.3.1", "resolved": "https://registry.npmjs.org/lodash.upperfirst/-/lodash.upperfirst-4.3.1.tgz", "integrity": "sha1-E2Xt9DFIBIHvDRxolXpe2Z1J984=", "dev": true}, "lodash.zip": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/lodash.zip/-/lodash.zip-4.2.0.tgz", "integrity": "sha1-7GZi5IlkCO1KtsVCo5kLcswIACA=", "dev": true}, "loose-envify": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.3.1.tgz", "integrity": "sha1-0aitM/qc4OcT1l/dCsi3SNR4yEg=", "dev": true, "requires": {"js-tokens": "^3.0.0"}, "dependencies": {"js-tokens": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-3.0.2.tgz", "integrity": "sha1-mGbfOVECEw449/mWvOtlRDIJwls=", "dev": true}}}, "mime": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg=="}, "mime-db": {"version": "1.36.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.36.0.tgz", "integrity": "sha512-L+xvyD9MkoYMXb1jAmzI/lWYAxAMCPvIBSWur0PZ5nOf5euahRLVqH//FKW9mWp2lkqUgYiXPgkzfMUFi4zVDw=="}, "mime-types": {"version": "2.1.20", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.20.tgz", "integrity": "sha512-HrkrPaP9vGuWbLK1B1FfgAkbqNjIuy4eHlIYnFi7kamZyLLrGlo2mpcx0bBmNpKqBtYtAfGbodDddIgddSJC2A==", "requires": {"mime-db": "~1.36.0"}}, "mimic-fn": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.2.0.tgz", "integrity": "sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==", "dev": true}, "minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "0.0.8", "resolved": "http://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0="}, "mkdirp": {"version": "0.5.1", "resolved": "http://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "requires": {"minimist": "0.0.8"}}, "mocha": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/mocha/-/mocha-5.1.1.tgz", "integrity": "sha512-kKKs/H1KrMMQIEsWNxGmb4/BGsmj0dkeyotEvbrAuQ01FcWRLssUNXCEUZk6SZtyJBi6EE7SL0zDDtItw1rGhw==", "dev": true, "requires": {"browser-stdout": "1.3.1", "commander": "2.11.0", "debug": "3.1.0", "diff": "3.5.0", "escape-string-regexp": "1.0.5", "glob": "7.1.2", "growl": "1.10.3", "he": "1.1.1", "minimatch": "3.0.4", "mkdirp": "0.5.1", "supports-color": "4.4.0"}, "dependencies": {"glob": {"version": "7.1.2", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.2.tgz", "integrity": "sha512-MJTUg1kjuLeQCJ+ccE4Vpa6kKVXkPYJ2mOCQyUuKLcLQsdrMCpBPUi8qVE6+YuaJkozeA9NusTAw3hLr8Xe5EQ==", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "has-flag": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-2.0.0.tgz", "integrity": "sha1-6CB68cx7MNRGzHC3NLXovhj4jVE=", "dev": true}, "supports-color": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-4.4.0.tgz", "integrity": "sha512-rKC3+DyXWgK0ZLKwmRsrkyHVZAjNkfzeehuFWdGGcqGDTZFH73+RH6S/RDAAxl9GusSjZSUWYLmT9N5pzXFOXQ==", "dev": true, "requires": {"has-flag": "^2.0.0"}}}}, "mock-stdin": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/mock-stdin/-/mock-stdin-0.3.1.tgz", "integrity": "sha1-xlfZZC2QeGQ1xkyl6Zu9TQm9fdM=", "dev": true}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "mute-stream": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.7.tgz", "integrity": "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=", "dev": true}, "natural-compare": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true}, "nice-try": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/nice-try/-/nice-try-1.0.5.tgz", "integrity": "sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ=="}, "nwmatcher": {"version": "1.4.4", "resolved": "https://registry.npmjs.org/nwmatcher/-/nwmatcher-1.4.4.tgz", "integrity": "sha512-3iuY4N5dhgMpCUrOVnuAdGrgxVqV2cJpM+XNccjR2DKOB1RUP0aA+wGXEiNziG/UKboFyGBIoKOaNlJxx8bciQ=="}, "nyc": {"version": "12.0.2", "resolved": "https://registry.npmjs.org/nyc/-/nyc-12.0.2.tgz", "integrity": "sha1-ikpO1pCWbBHsWH/4fuoMEsl0upk=", "dev": true, "requires": {"archy": "^1.0.0", "arrify": "^1.0.1", "caching-transform": "^1.0.0", "convert-source-map": "^1.5.1", "debug-log": "^1.0.1", "default-require-extensions": "^1.0.0", "find-cache-dir": "^0.1.1", "find-up": "^2.1.0", "foreground-child": "^1.5.3", "glob": "^7.0.6", "istanbul-lib-coverage": "^1.2.0", "istanbul-lib-hook": "^1.1.0", "istanbul-lib-instrument": "^2.1.0", "istanbul-lib-report": "^1.1.3", "istanbul-lib-source-maps": "^1.2.5", "istanbul-reports": "^1.4.1", "md5-hex": "^1.2.0", "merge-source-map": "^1.1.0", "micromatch": "^3.1.10", "mkdirp": "^0.5.0", "resolve-from": "^2.0.0", "rimraf": "^2.6.2", "signal-exit": "^3.0.1", "spawn-wrap": "^1.4.2", "test-exclude": "^4.2.0", "yargs": "11.1.0", "yargs-parser": "^8.0.0"}, "dependencies": {"align-text": {"version": "0.1.4", "bundled": true, "dev": true, "requires": {"kind-of": "^3.0.2", "longest": "^1.0.1", "repeat-string": "^1.5.2"}}, "amdefine": {"version": "1.0.1", "bundled": true, "dev": true}, "ansi-regex": {"version": "3.0.0", "bundled": true, "dev": true}, "append-transform": {"version": "0.4.0", "bundled": true, "dev": true, "requires": {"default-require-extensions": "^1.0.0"}}, "archy": {"version": "1.0.0", "bundled": true, "dev": true}, "arr-diff": {"version": "4.0.0", "bundled": true, "dev": true}, "arr-flatten": {"version": "1.1.0", "bundled": true, "dev": true}, "arr-union": {"version": "3.1.0", "bundled": true, "dev": true}, "array-unique": {"version": "0.3.2", "bundled": true, "dev": true}, "arrify": {"version": "1.0.1", "bundled": true, "dev": true}, "assign-symbols": {"version": "1.0.0", "bundled": true, "dev": true}, "async": {"version": "1.5.2", "bundled": true, "dev": true}, "atob": {"version": "2.1.1", "bundled": true, "dev": true}, "balanced-match": {"version": "1.0.0", "bundled": true, "dev": true}, "base": {"version": "0.11.2", "bundled": true, "dev": true, "requires": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "dependencies": {"define-property": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}, "kind-of": {"version": "6.0.2", "bundled": true, "dev": true}}}, "brace-expansion": {"version": "1.1.11", "bundled": true, "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "2.3.2", "bundled": true, "dev": true, "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "builtin-modules": {"version": "1.1.1", "bundled": true, "dev": true}, "cache-base": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}}, "caching-transform": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"md5-hex": "^1.2.0", "mkdirp": "^0.5.1", "write-file-atomic": "^1.1.4"}}, "camelcase": {"version": "1.2.1", "bundled": true, "dev": true, "optional": true}, "center-align": {"version": "0.1.3", "bundled": true, "dev": true, "optional": true, "requires": {"align-text": "^0.1.3", "lazy-cache": "^1.0.3"}}, "class-utils": {"version": "0.3.6", "bundled": true, "dev": true, "requires": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "dependencies": {"define-property": {"version": "0.2.5", "bundled": true, "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "cliui": {"version": "2.1.0", "bundled": true, "dev": true, "optional": true, "requires": {"center-align": "^0.1.1", "right-align": "^0.1.1", "wordwrap": "0.0.2"}, "dependencies": {"wordwrap": {"version": "0.0.2", "bundled": true, "dev": true, "optional": true}}}, "code-point-at": {"version": "1.1.0", "bundled": true, "dev": true}, "collection-visit": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}}, "commondir": {"version": "1.0.1", "bundled": true, "dev": true}, "component-emitter": {"version": "1.2.1", "bundled": true, "dev": true}, "concat-map": {"version": "0.0.1", "bundled": true, "dev": true}, "convert-source-map": {"version": "1.5.1", "bundled": true, "dev": true}, "copy-descriptor": {"version": "0.1.1", "bundled": true, "dev": true}, "cross-spawn": {"version": "4.0.2", "bundled": true, "dev": true, "requires": {"lru-cache": "^4.0.1", "which": "^1.2.9"}}, "debug": {"version": "3.1.0", "bundled": true, "dev": true, "requires": {"ms": "2.0.0"}}, "debug-log": {"version": "1.0.1", "bundled": true, "dev": true}, "decamelize": {"version": "1.2.0", "bundled": true, "dev": true}, "decode-uri-component": {"version": "0.2.0", "bundled": true, "dev": true}, "default-require-extensions": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"strip-bom": "^2.0.0"}}, "define-property": {"version": "2.0.2", "bundled": true, "dev": true, "requires": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "dependencies": {"is-accessor-descriptor": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}, "kind-of": {"version": "6.0.2", "bundled": true, "dev": true}}}, "error-ex": {"version": "1.3.1", "bundled": true, "dev": true, "requires": {"is-arrayish": "^0.2.1"}}, "execa": {"version": "0.7.0", "bundled": true, "dev": true, "requires": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "dependencies": {"cross-spawn": {"version": "5.1.0", "bundled": true, "dev": true, "requires": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}}}, "expand-brackets": {"version": "2.1.4", "bundled": true, "dev": true, "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "bundled": true, "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "bundled": true, "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "extend-shallow": {"version": "3.0.2", "bundled": true, "dev": true, "requires": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"is-plain-object": "^2.0.4"}}}}, "extglob": {"version": "2.0.4", "bundled": true, "dev": true, "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}, "kind-of": {"version": "6.0.2", "bundled": true, "dev": true}}}, "fill-range": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "find-cache-dir": {"version": "0.1.1", "bundled": true, "dev": true, "requires": {"commondir": "^1.0.1", "mkdirp": "^0.5.1", "pkg-dir": "^1.0.0"}}, "find-up": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"locate-path": "^2.0.0"}}, "for-in": {"version": "1.0.2", "bundled": true, "dev": true}, "foreground-child": {"version": "1.5.6", "bundled": true, "dev": true, "requires": {"cross-spawn": "^4", "signal-exit": "^3.0.0"}}, "fragment-cache": {"version": "0.2.1", "bundled": true, "dev": true, "requires": {"map-cache": "^0.2.2"}}, "fs.realpath": {"version": "1.0.0", "bundled": true, "dev": true}, "get-caller-file": {"version": "1.0.2", "bundled": true, "dev": true}, "get-stream": {"version": "3.0.0", "bundled": true, "dev": true}, "get-value": {"version": "2.0.6", "bundled": true, "dev": true}, "glob": {"version": "7.1.2", "bundled": true, "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "graceful-fs": {"version": "4.1.11", "bundled": true, "dev": true}, "handlebars": {"version": "4.0.11", "bundled": true, "dev": true, "requires": {"async": "^1.4.0", "optimist": "^0.6.1", "source-map": "^0.4.4", "uglify-js": "^2.6"}, "dependencies": {"source-map": {"version": "0.4.4", "bundled": true, "dev": true, "requires": {"amdefine": ">=0.0.4"}}}}, "has-value": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}}, "has-values": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"kind-of": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "hosted-git-info": {"version": "2.6.0", "bundled": true, "dev": true}, "imurmurhash": {"version": "0.1.4", "bundled": true, "dev": true}, "inflight": {"version": "1.0.6", "bundled": true, "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3", "bundled": true, "dev": true}, "invert-kv": {"version": "1.0.0", "bundled": true, "dev": true}, "is-accessor-descriptor": {"version": "0.1.6", "bundled": true, "dev": true, "requires": {"kind-of": "^3.0.2"}}, "is-arrayish": {"version": "0.2.1", "bundled": true, "dev": true}, "is-buffer": {"version": "1.1.6", "bundled": true, "dev": true}, "is-builtin-module": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"builtin-modules": "^1.0.0"}}, "is-data-descriptor": {"version": "0.1.4", "bundled": true, "dev": true, "requires": {"kind-of": "^3.0.2"}}, "is-descriptor": {"version": "0.1.6", "bundled": true, "dev": true, "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "dependencies": {"kind-of": {"version": "5.1.0", "bundled": true, "dev": true}}}, "is-extendable": {"version": "0.1.1", "bundled": true, "dev": true}, "is-fullwidth-code-point": {"version": "2.0.0", "bundled": true, "dev": true}, "is-number": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"kind-of": "^3.0.2"}}, "is-odd": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"is-number": "^4.0.0"}, "dependencies": {"is-number": {"version": "4.0.0", "bundled": true, "dev": true}}}, "is-plain-object": {"version": "2.0.4", "bundled": true, "dev": true, "requires": {"isobject": "^3.0.1"}}, "is-stream": {"version": "1.1.0", "bundled": true, "dev": true}, "is-utf8": {"version": "0.2.1", "bundled": true, "dev": true}, "is-windows": {"version": "1.0.2", "bundled": true, "dev": true}, "isarray": {"version": "1.0.0", "bundled": true, "dev": true}, "isexe": {"version": "2.0.0", "bundled": true, "dev": true}, "isobject": {"version": "3.0.1", "bundled": true, "dev": true}, "istanbul-lib-coverage": {"version": "1.2.0", "bundled": true, "dev": true}, "istanbul-lib-hook": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"append-transform": "^0.4.0"}}, "istanbul-lib-report": {"version": "1.1.3", "bundled": true, "dev": true, "requires": {"istanbul-lib-coverage": "^1.1.2", "mkdirp": "^0.5.1", "path-parse": "^1.0.5", "supports-color": "^3.1.2"}, "dependencies": {"has-flag": {"version": "1.0.0", "bundled": true, "dev": true}, "supports-color": {"version": "3.2.3", "bundled": true, "dev": true, "requires": {"has-flag": "^1.0.0"}}}}, "istanbul-lib-source-maps": {"version": "1.2.5", "bundled": true, "dev": true, "requires": {"debug": "^3.1.0", "istanbul-lib-coverage": "^1.2.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "source-map": "^0.5.3"}}, "istanbul-reports": {"version": "1.4.1", "bundled": true, "dev": true, "requires": {"handlebars": "^4.0.3"}}, "kind-of": {"version": "3.2.2", "bundled": true, "dev": true, "requires": {"is-buffer": "^1.1.5"}}, "lazy-cache": {"version": "1.0.4", "bundled": true, "dev": true, "optional": true}, "lcid": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"invert-kv": "^1.0.0"}}, "load-json-file": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "strip-bom": "^2.0.0"}}, "locate-path": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}, "dependencies": {"path-exists": {"version": "3.0.0", "bundled": true, "dev": true}}}, "longest": {"version": "1.0.1", "bundled": true, "dev": true}, "lru-cache": {"version": "4.1.3", "bundled": true, "dev": true, "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "map-cache": {"version": "0.2.2", "bundled": true, "dev": true}, "map-visit": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"object-visit": "^1.0.0"}}, "md5-hex": {"version": "1.3.0", "bundled": true, "dev": true, "requires": {"md5-o-matic": "^0.1.1"}}, "md5-o-matic": {"version": "0.1.1", "bundled": true, "dev": true}, "mem": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"mimic-fn": "^1.0.0"}}, "merge-source-map": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"source-map": "^0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "bundled": true, "dev": true}}}, "micromatch": {"version": "3.1.10", "bundled": true, "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "dependencies": {"kind-of": {"version": "6.0.2", "bundled": true, "dev": true}}}, "mimic-fn": {"version": "1.2.0", "bundled": true, "dev": true}, "minimatch": {"version": "3.0.4", "bundled": true, "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "0.0.8", "bundled": true, "dev": true}, "mixin-deep": {"version": "1.3.1", "bundled": true, "dev": true, "requires": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"is-plain-object": "^2.0.4"}}}}, "mkdirp": {"version": "0.5.1", "bundled": true, "dev": true, "requires": {"minimist": "0.0.8"}}, "ms": {"version": "2.0.0", "bundled": true, "dev": true}, "nanomatch": {"version": "1.2.9", "bundled": true, "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-odd": "^2.0.0", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"kind-of": {"version": "6.0.2", "bundled": true, "dev": true}}}, "normalize-package-data": {"version": "2.4.0", "bundled": true, "dev": true, "requires": {"hosted-git-info": "^2.1.4", "is-builtin-module": "^1.0.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "npm-run-path": {"version": "2.0.2", "bundled": true, "dev": true, "requires": {"path-key": "^2.0.0"}}, "number-is-nan": {"version": "1.0.1", "bundled": true, "dev": true}, "object-assign": {"version": "4.1.1", "bundled": true, "dev": true}, "object-copy": {"version": "0.1.0", "bundled": true, "dev": true, "requires": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "dependencies": {"define-property": {"version": "0.2.5", "bundled": true, "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "object-visit": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"isobject": "^3.0.0"}}, "object.pick": {"version": "1.3.0", "bundled": true, "dev": true, "requires": {"isobject": "^3.0.1"}}, "once": {"version": "1.4.0", "bundled": true, "dev": true, "requires": {"wrappy": "1"}}, "optimist": {"version": "0.6.1", "bundled": true, "dev": true, "requires": {"minimist": "~0.0.1", "wordwrap": "~0.0.2"}}, "os-homedir": {"version": "1.0.2", "bundled": true, "dev": true}, "os-locale": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"execa": "^0.7.0", "lcid": "^1.0.0", "mem": "^1.1.0"}}, "p-finally": {"version": "1.0.0", "bundled": true, "dev": true}, "p-limit": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"p-try": "^1.0.0"}}, "p-locate": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"p-limit": "^1.1.0"}}, "p-try": {"version": "1.0.0", "bundled": true, "dev": true}, "parse-json": {"version": "2.2.0", "bundled": true, "dev": true, "requires": {"error-ex": "^1.2.0"}}, "pascalcase": {"version": "0.1.1", "bundled": true, "dev": true}, "path-exists": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"pinkie-promise": "^2.0.0"}}, "path-is-absolute": {"version": "1.0.1", "bundled": true, "dev": true}, "path-key": {"version": "2.0.1", "bundled": true, "dev": true}, "path-parse": {"version": "1.0.5", "bundled": true, "dev": true}, "path-type": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.2", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "pify": {"version": "2.3.0", "bundled": true, "dev": true}, "pinkie": {"version": "2.0.4", "bundled": true, "dev": true}, "pinkie-promise": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"pinkie": "^2.0.0"}}, "pkg-dir": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"find-up": "^1.0.0"}, "dependencies": {"find-up": {"version": "1.1.2", "bundled": true, "dev": true, "requires": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}}}}, "posix-character-classes": {"version": "0.1.1", "bundled": true, "dev": true}, "pseudomap": {"version": "1.0.2", "bundled": true, "dev": true}, "read-pkg": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"load-json-file": "^1.0.0", "normalize-package-data": "^2.3.2", "path-type": "^1.0.0"}}, "read-pkg-up": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"find-up": "^1.0.0", "read-pkg": "^1.0.0"}, "dependencies": {"find-up": {"version": "1.1.2", "bundled": true, "dev": true, "requires": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}}}}, "regex-not": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}}, "repeat-element": {"version": "1.1.2", "bundled": true, "dev": true}, "repeat-string": {"version": "1.6.1", "bundled": true, "dev": true}, "require-directory": {"version": "2.1.1", "bundled": true, "dev": true}, "require-main-filename": {"version": "1.0.1", "bundled": true, "dev": true}, "resolve-from": {"version": "2.0.0", "bundled": true, "dev": true}, "resolve-url": {"version": "0.2.1", "bundled": true, "dev": true}, "ret": {"version": "0.1.15", "bundled": true, "dev": true}, "right-align": {"version": "0.1.3", "bundled": true, "dev": true, "optional": true, "requires": {"align-text": "^0.1.1"}}, "rimraf": {"version": "2.6.2", "bundled": true, "dev": true, "requires": {"glob": "^7.0.5"}}, "safe-regex": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"ret": "~0.1.10"}}, "semver": {"version": "5.5.0", "bundled": true, "dev": true}, "set-blocking": {"version": "2.0.0", "bundled": true, "dev": true}, "set-value": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "shebang-command": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "bundled": true, "dev": true}, "signal-exit": {"version": "3.0.2", "bundled": true, "dev": true}, "slide": {"version": "1.1.6", "bundled": true, "dev": true}, "snapdragon": {"version": "0.8.2", "bundled": true, "dev": true, "requires": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "dependencies": {"debug": {"version": "2.6.9", "bundled": true, "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "bundled": true, "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "snapdragon-node": {"version": "2.1.1", "bundled": true, "dev": true, "requires": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}, "kind-of": {"version": "6.0.2", "bundled": true, "dev": true}}}, "snapdragon-util": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"kind-of": "^3.2.0"}}, "source-map": {"version": "0.5.7", "bundled": true, "dev": true}, "source-map-resolve": {"version": "0.5.2", "bundled": true, "dev": true, "requires": {"atob": "^2.1.1", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "source-map-url": {"version": "0.4.0", "bundled": true, "dev": true}, "spawn-wrap": {"version": "1.4.2", "bundled": true, "dev": true, "requires": {"foreground-child": "^1.5.6", "mkdirp": "^0.5.0", "os-homedir": "^1.0.1", "rimraf": "^2.6.2", "signal-exit": "^3.0.2", "which": "^1.3.0"}}, "spdx-correct": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.1.0", "bundled": true, "dev": true}, "spdx-expression-parse": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.0", "bundled": true, "dev": true}, "split-string": {"version": "3.1.0", "bundled": true, "dev": true, "requires": {"extend-shallow": "^3.0.0"}}, "static-extend": {"version": "0.1.2", "bundled": true, "dev": true, "requires": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "bundled": true, "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "string-width": {"version": "2.1.1", "bundled": true, "dev": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "strip-ansi": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"ansi-regex": "^3.0.0"}}, "strip-bom": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"is-utf8": "^0.2.0"}}, "strip-eof": {"version": "1.0.0", "bundled": true, "dev": true}, "test-exclude": {"version": "4.2.1", "bundled": true, "dev": true, "requires": {"arrify": "^1.0.1", "micromatch": "^3.1.8", "object-assign": "^4.1.0", "read-pkg-up": "^1.0.1", "require-main-filename": "^1.0.1"}}, "to-object-path": {"version": "0.3.0", "bundled": true, "dev": true, "requires": {"kind-of": "^3.0.2"}}, "to-regex": {"version": "3.0.2", "bundled": true, "dev": true, "requires": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}}, "to-regex-range": {"version": "2.1.1", "bundled": true, "dev": true, "requires": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}}, "uglify-js": {"version": "2.8.29", "bundled": true, "dev": true, "optional": true, "requires": {"source-map": "~0.5.1", "uglify-to-browserify": "~1.0.0", "yargs": "~3.10.0"}, "dependencies": {"yargs": {"version": "3.10.0", "bundled": true, "dev": true, "optional": true, "requires": {"camelcase": "^1.0.2", "cliui": "^2.1.0", "decamelize": "^1.0.0", "window-size": "0.1.0"}}}}, "uglify-to-browserify": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "union-value": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^0.4.3"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "set-value": {"version": "0.4.3", "bundled": true, "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.1", "to-object-path": "^0.3.0"}}}}, "unset-value": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "dependencies": {"has-value": {"version": "0.3.1", "bundled": true, "dev": true, "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4", "bundled": true, "dev": true}}}, "urix": {"version": "0.1.0", "bundled": true, "dev": true}, "use": {"version": "3.1.0", "bundled": true, "dev": true, "requires": {"kind-of": "^6.0.2"}, "dependencies": {"kind-of": {"version": "6.0.2", "bundled": true, "dev": true}}}, "validate-npm-package-license": {"version": "3.0.3", "bundled": true, "dev": true, "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "which": {"version": "1.3.1", "bundled": true, "dev": true, "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "2.0.0", "bundled": true, "dev": true}, "window-size": {"version": "0.1.0", "bundled": true, "dev": true, "optional": true}, "wordwrap": {"version": "0.0.3", "bundled": true, "dev": true}, "wrap-ansi": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "bundled": true, "dev": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"number-is-nan": "^1.0.0"}}, "string-width": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"ansi-regex": "^2.0.0"}}}}, "wrappy": {"version": "1.0.2", "bundled": true, "dev": true}, "write-file-atomic": {"version": "1.3.4", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "slide": "^1.1.5"}}, "y18n": {"version": "3.2.1", "bundled": true, "dev": true}, "yallist": {"version": "2.1.2", "bundled": true, "dev": true}, "yargs": {"version": "11.1.0", "bundled": true, "dev": true, "requires": {"cliui": "^4.0.0", "decamelize": "^1.1.1", "find-up": "^2.1.0", "get-caller-file": "^1.0.1", "os-locale": "^2.0.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1", "yargs-parser": "^9.0.2"}, "dependencies": {"camelcase": {"version": "4.1.0", "bundled": true, "dev": true}, "cliui": {"version": "4.1.0", "bundled": true, "dev": true, "requires": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0", "wrap-ansi": "^2.0.0"}}, "yargs-parser": {"version": "9.0.2", "bundled": true, "dev": true, "requires": {"camelcase": "^4.1.0"}}}}, "yargs-parser": {"version": "8.1.0", "bundled": true, "dev": true, "requires": {"camelcase": "^4.1.0"}, "dependencies": {"camelcase": {"version": "4.1.0", "bundled": true, "dev": true}}}}}, "oauth-sign": {"version": "0.9.0", "resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz", "integrity": "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ=="}, "object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true}, "once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "requires": {"wrappy": "1"}}, "onetime": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz", "integrity": "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=", "dev": true, "requires": {"mimic-fn": "^1.0.0"}}, "optionator": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/optionator/-/optionator-0.8.2.tgz", "integrity": "sha1-NkxeQJ0/TWMB1sC0wFu6UBgK62Q=", "requires": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.4", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "wordwrap": "~1.0.0"}}, "os-tmpdir": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=", "dev": true}, "parse5": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/parse5/-/parse5-1.5.1.tgz", "integrity": "sha1-m387DeMr543CQBsXVzzK8Pb1nZQ="}, "password-prompt": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/password-prompt/-/password-prompt-1.0.7.tgz", "integrity": "sha1-jid0jTQAvJyRQNWt5wXft6632Ro=", "requires": {"ansi-escapes": "^3.1.0", "cross-spawn": "^6.0.5"}}, "path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="}, "path-is-inside": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/path-is-inside/-/path-is-inside-1.0.2.tgz", "integrity": "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=", "dev": true}, "path-key": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="}, "path-parse": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.6.tgz", "integrity": "sha512-GSmOT2EbHrINBf9SR7CDELwlJ8AENk3Qn7OikK4nFYAu3Ote2+JYNVvkpAEQm3/TLNEJFD/xZJjzyxg3KBWOzw==", "dev": true}, "pathval": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/pathval/-/pathval-1.1.0.tgz", "integrity": "sha1-uULm1L3mUwBe9rcTYd74cn0GReA=", "dev": true}, "pend": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz", "integrity": "sha1-elfrVQpng/kRUzH89GY9XI4AelA="}, "performance-now": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="}, "pify": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true}, "pinkie": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz", "integrity": "sha1-clVrgM+g1IqXToDnckjoDtT3+HA=", "dev": true}, "pinkie-promise": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz", "integrity": "sha1-ITXW36ejWMBprJsXh3YogihFD/o=", "dev": true, "requires": {"pinkie": "^2.0.0"}}, "pluralize": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/pluralize/-/pluralize-7.0.0.tgz", "integrity": "sha512-ARhBOdzS3e41FbkW/XWrTEtukqqLoK5+Z/4UeDaLuSW+39JPeFgs4gCGqsrJHVZX0fUrx//4OF0K1CUGwlIFow==", "dev": true}, "prelude-ls": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz", "integrity": "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ="}, "process-nextick-args": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.0.tgz", "integrity": "sha512-MtEC1TqN0EU5nephaJ4rAtThHtC86dNN9qCuEhtshvpVBkAW5ZO7BASN9REnF9eoXGcRub+pFuKEpOHE+HbEMw=="}, "progress": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/progress/-/progress-2.0.0.tgz", "integrity": "sha1-ihvjZr+Pwj2yvSPxDG/pILQ4nR8="}, "proxy-from-env": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.0.0.tgz", "integrity": "sha1-M8UDmPcOp+uW0h97gXYwpVeRx+4="}, "psl": {"version": "1.1.29", "resolved": "https://registry.npmjs.org/psl/-/psl-1.1.29.tgz", "integrity": "sha512-AeUmQ0oLN02flVHXWh9sSJF7mcdFq0ppid/JkErufc3hGIV/AMa8Fo9VgDo/cT2jFdOWoFvHp90qqBH54W+gjQ=="}, "punycode": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz", "integrity": "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==", "dev": true}, "puppeteer": {"version": "0.10.1", "resolved": "https://registry.npmjs.org/puppeteer/-/puppeteer-0.10.1.tgz", "integrity": "sha1-om9wEv8PzJ8hxw/54qjGDLpWioM=", "requires": {"debug": "^2.6.8", "extract-zip": "^1.6.5", "https-proxy-agent": "^2.1.0", "mime": "^1.3.4", "progress": "^2.0.0", "proxy-from-env": "^1.0.0", "rimraf": "^2.6.1", "ws": "^3.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}}}, "qs": {"version": "6.5.2", "resolved": "https://registry.npmjs.org/qs/-/qs-6.5.2.tgz", "integrity": "sha512-N5ZAX4/LxJmF+7wN74pUD6qAh9/wnvdQcjq9TZjevvXzSUo7bfmw91saqMjzGS2xq91/odN2dW/WOl7qQHNDGA=="}, "ramda": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/ramda/-/ramda-0.25.0.tgz", "integrity": "sha512-GXpfrYVPwx3K7RQ6aYT8KPS8XViSXUVJT1ONhoKPE9VAleW42YE+U+8VEyGWt41EnEQW7gwecYJriTI0pKoecQ==", "dev": true}, "readable-stream": {"version": "2.3.6", "resolved": "http://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw==", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "redeyed": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/redeyed/-/redeyed-2.1.1.tgz", "integrity": "sha1-iYS1gV2ZyyIEacme7v/jiRPmzAs=", "requires": {"esprima": "~4.0.0"}}, "regexpp": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/regexpp/-/regexpp-2.0.0.tgz", "integrity": "sha512-g2FAVtR8Uh8GO1Nv5wpxW7VFVwHcCEr4wyA8/MHiRkO8uHoR5ntAA8Uq3P1vvMTX/BeQiRVSpDGLd+Wn5HNOTA==", "dev": true}, "request": {"version": "2.88.0", "resolved": "https://registry.npmjs.org/request/-/request-2.88.0.tgz", "integrity": "sha512-NAqBSrijGLZdM0WZNsInLJpkJokL72XYjUpnB0iwsRgxh7dB6COrHnTBNwN0E+lHDAJzu7kLAkDeY08z2/A0hg==", "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.0", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.4.3", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}}, "require-uncached": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/require-uncached/-/require-uncached-1.0.3.tgz", "integrity": "sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM=", "dev": true, "requires": {"caller-path": "^0.1.0", "resolve-from": "^1.0.0"}}, "resolve": {"version": "1.8.1", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.8.1.tgz", "integrity": "sha512-AicPrAC7Qu1JxPCZ9ZgCZlY35QgFnNqc+0LtbRNxnVw4TXvjQ72wnuL9JQcEBgXkI9JM8MsT9kaQoHcpCRJOYA==", "dev": true, "requires": {"path-parse": "^1.0.5"}}, "resolve-from": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-1.0.1.tgz", "integrity": "sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY=", "dev": true}, "restore-cursor": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz", "integrity": "sha1-n37ih/gv0ybU/RYpI9YhKe7g368=", "dev": true, "requires": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}}, "ret": {"version": "0.1.15", "resolved": "https://registry.npmjs.org/ret/-/ret-0.1.15.tgz", "integrity": "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg==", "dev": true}, "rimraf": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.6.2.tgz", "integrity": "sha512-lreewLK/BlghmxtfH36YYVg1i8IAce4TI7oao75I1g245+6BctqTVQiBP3YUJ9C6DQOXJmkYR9X9fCLtCOJc5w==", "requires": {"glob": "^7.0.5"}}, "run-async": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/run-async/-/run-async-2.3.0.tgz", "integrity": "sha1-A3GrSuC91yDUFm19/aZP96RFpsA=", "dev": true, "requires": {"is-promise": "^2.1.0"}}, "rw": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/rw/-/rw-1.3.3.tgz", "integrity": "sha1-P4Yt+pGrdmsUiF700BEkv9oHT7Q="}, "rxjs": {"version": "6.3.2", "resolved": "https://registry.npmjs.org/rxjs/-/rxjs-6.3.2.tgz", "integrity": "sha512-hV7criqbR0pe7EeL3O66UYVg92IR0XsA97+9y+BWTePK9SKmEI5Qd3Zj6uPnGkNzXsBywBQWTvujPl+1Kn9Zjw==", "dev": true, "requires": {"tslib": "^1.9.0"}}, "safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="}, "safe-regex": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/safe-regex/-/safe-regex-1.1.0.tgz", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "dev": true, "requires": {"ret": "~0.1.10"}}, "safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "sax": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz", "integrity": "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="}, "semver": {"version": "5.5.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.5.1.tgz", "integrity": "sha512-PqpAxfrEhlSUWge8dwIp4tZnQ25DIOthpiaHNIthsjEFQD6EvqUKUDM7L8O2rShkFccYo1VjJR0coWfNkCubRw=="}, "shebang-command": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="}, "signal-exit": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.2.tgz", "integrity": "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0=", "dev": true}, "slice-ansi": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/slice-ansi/-/slice-ansi-1.0.0.tgz", "integrity": "sha512-POqxBK6Lb3q6s047D/XsDVNPnF9Dl8JSaqe9h9lURl0OdNqy/ujDrOiIHtsqXMGbWWTIomRzAMaTyawAU//Reg==", "dev": true, "requires": {"is-fullwidth-code-point": "^2.0.0"}}, "source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}, "sprintf-js": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=", "dev": true}, "sshpk": {"version": "1.14.2", "resolved": "https://registry.npmjs.org/sshpk/-/sshpk-1.14.2.tgz", "integrity": "sha1-xvxhZIo9nE52T9P8306hBeSSupg=", "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}}, "stdout-stderr": {"version": "0.1.9", "resolved": "https://registry.npmjs.org/stdout-stderr/-/stdout-stderr-0.1.9.tgz", "integrity": "sha1-m0juBO/5Ve4Hd24nEl1VJNnQL1c=", "dev": true, "requires": {"debug": "^3.1.0", "strip-ansi": "^4.0.0"}}, "string-width": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/string-width/-/string-width-2.1.1.tgz", "integrity": "sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==", "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "requires": {"safe-buffer": "~5.1.0"}}, "strip-ansi": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "requires": {"ansi-regex": "^3.0.0"}}, "strip-json-comments": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "integrity": "sha1-PFMZQukIwml8DsNEhYwobHygpgo=", "dev": true}, "supports-color": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "requires": {"has-flag": "^3.0.0"}}, "supports-hyperlinks": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-1.0.1.tgz", "integrity": "sha512-HHi5kVSefKaJkGYXbDuKbUGRVxqnWGn3J2e39CYcNJEfWciGq2zYtOhXLTlvrOZW1QU7VX67w7fMmWafHX9Pfw==", "requires": {"has-flag": "^2.0.0", "supports-color": "^5.0.0"}, "dependencies": {"has-flag": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-2.0.0.tgz", "integrity": "sha1-6CB68cx7MNRGzHC3NLXovhj4jVE="}}}, "symbol-tree": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/symbol-tree/-/symbol-tree-3.2.2.tgz", "integrity": "sha1-rifbOPZgp64uHDt9G8KQgZuFGeY="}, "table": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/table/-/table-4.0.3.tgz", "integrity": "sha512-S7rnFITmBH1EnyKcvxBh1LjYeQMmnZtCXSEbHcH6S0NoKit24ZuFO/T1vDcLdYsLQkM188PVVhQmzKIuThNkKg==", "dev": true, "requires": {"ajv": "^6.0.1", "ajv-keywords": "^3.0.0", "chalk": "^2.1.0", "lodash": "^4.17.4", "slice-ansi": "1.0.0", "string-width": "^2.1.1"}}, "text-table": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=", "dev": true}, "through": {"version": "2.3.8", "resolved": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=", "dev": true}, "tmp": {"version": "0.0.33", "resolved": "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz", "integrity": "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==", "dev": true, "requires": {"os-tmpdir": "~1.0.2"}}, "to-fast-properties": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "integrity": "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=", "dev": true}, "tough-cookie": {"version": "2.4.3", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.4.3.tgz", "integrity": "sha512-Q5srk/4vDM54WJsJio3XNn6K2sCG+CQ8G5Wz6bZhRZoAe/+TxjWB/GlFAnYEbkYVlON9FMk/fE3h2RLpPXo4lQ==", "requires": {"psl": "^1.1.24", "punycode": "^1.4.1"}, "dependencies": {"punycode": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4="}}}, "tr46": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o="}, "trim-right": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/trim-right/-/trim-right-1.0.1.tgz", "integrity": "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=", "dev": true}, "tslib": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.9.3.tgz", "integrity": "sha512-4krF8scpejhaOgqzBEcGM7yDIEfi0/8+8zDRZhNZZ2kjmHJ4hv3zCbQWxoJGz1iw5U0Jl0nma13xzHXcncMavQ=="}, "tunnel-agent": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "requires": {"safe-buffer": "^5.0.1"}}, "tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=", "optional": true}, "type-check": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz", "integrity": "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=", "requires": {"prelude-ls": "~1.1.2"}}, "type-detect": {"version": "4.0.8", "resolved": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz", "integrity": "sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==", "dev": true}, "typedarray": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="}, "ultron": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ultron/-/ultron-1.1.1.tgz", "integrity": "sha512-UIEXBNeYmKptWH6z8ZnqTeS8fV74zG0/eRU9VGkpzz+LIJNs8W/zM/L+7ctCkRrgbNnnR0xxw4bKOr0cW0N0Og=="}, "universalify": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz", "integrity": "sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg=="}, "uri-js": {"version": "4.2.2", "resolved": "https://registry.npmjs.org/uri-js/-/uri-js-4.2.2.tgz", "integrity": "sha512-KY9Frmirql91X2Qgjry0Wd4Y+YTdrdZheS8TFwvkbLWf/G5KNJDCh6pKL5OZctEW4+0Baa5idK2ZQuELRwPznQ==", "dev": true, "requires": {"punycode": "^2.1.0"}}, "util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="}, "uuid": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-3.3.2.tgz", "integrity": "sha512-yXJmeNaw3DnnKAOKJE51sL/ZaYfWJRl1pK9dr19YFCu0ObS231AB1/LbqTKRAQ5kw8A90rA6fr4riOUpTZvQZA=="}, "verror": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "webidl-conversions": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-4.0.2.tgz", "integrity": "sha512-YQ+BmxuTgd6UXZW3+ICGfyqRyHXVlD5GtQr5+qjiNW7bF0cqrzX500HVXPBOvgXb5YnzDd+h0zqyv61KUD7+Sg=="}, "whatwg-encoding": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.4.tgz", "integrity": "sha512-vM9KWN6MP2mIHZ86ytcyIv7e8Cj3KTfO2nd2c8PFDqcI4bxFmQp83ibq4wadq7rL9l9sZV6o9B0LTt8ygGAAXg==", "requires": {"iconv-lite": "0.4.23"}, "dependencies": {"iconv-lite": {"version": "0.4.23", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.23.tgz", "integrity": "sha512-neyTUVFtahjf0mB3dZT77u+8O0QB89jFdnBkd5P1JgYPbPaia3gXXOVL2fq8VyU2gMMD7SaN7QukTB/pmXYvDA==", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}}}, "whatwg-url": {"version": "4.8.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-4.8.0.tgz", "integrity": "sha1-0pgaqRSMHgCkHFphMRZqtGg7vMA=", "requires": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "dependencies": {"webidl-conversions": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE="}}}, "which": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/which/-/which-1.3.1.tgz", "integrity": "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==", "requires": {"isexe": "^2.0.0"}}, "widest-line": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/widest-line/-/widest-line-2.0.0.tgz", "integrity": "sha1-AUKk6KJD+IgsAjOqDgKBqnYVInM=", "requires": {"string-width": "^2.1.1"}}, "wordwrap": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz", "integrity": "sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus="}, "wrap-ansi": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-3.0.1.tgz", "integrity": "sha1-KIoE2H7aXChuBg3+jxNc6NAH+Lo=", "requires": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0"}}, "wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "write": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/write/-/write-0.2.1.tgz", "integrity": "sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c=", "dev": true, "requires": {"mkdirp": "^0.5.1"}}, "ws": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/ws/-/ws-3.3.3.tgz", "integrity": "sha512-nnWLa/NwZSt4KQJu51MYlCcSQ5g7INpOrOMt4XV8j4dqTXdmlUmSHQ8/oLC069ckre0fRsgfvsKwbTdtKLCDkA==", "requires": {"async-limiter": "~1.0.0", "safe-buffer": "~5.1.0", "ultron": "~1.1.0"}}, "xml-name-validator": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-2.0.1.tgz", "integrity": "sha1-TYuPHszTQZqjYgYb7O9RXh5VljU="}, "yauzl": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/yauzl/-/yauzl-2.4.1.tgz", "integrity": "sha1-lSj0QtqxsihOWLQ3m7GU4i4MQAU=", "requires": {"fd-slicer": "~1.0.1"}}}}