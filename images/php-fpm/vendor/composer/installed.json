[{"name": "psr/http-message", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T14:39:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"]}, {"name": "psr/log", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-10-10T12:19:37+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"]}, {"name": "monolog/monolog", "version": "1.23.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "fd8c787753b3a2ad11bc60c063cff1358a32a3b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/fd8c787753b3a2ad11bc60c063cff1358a32a3b4", "reference": "fd8c787753b3a2ad11bc60c063cff1358a32a3b4", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "jakub-onderka/php-parallel-lint": "0.9", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpunit/phpunit": "~4.5", "phpunit/phpunit-mock-objects": "2.3.0", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "time": "2017-06-19T01:22:40+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"]}, {"name": "guzzlehttp/psr7", "version": "1.4.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "f5b8a8512e2b58b0071a7280e39f14f72e05d87c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/f5b8a8512e2b58b0071a7280e39f14f72e05d87c", "reference": "f5b8a8512e2b58b0071a7280e39f14f72e05d87c", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "time": "2017-03-20T17:10:46+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "request", "response", "stream", "uri", "url"]}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "time": "2016-12-20T10:07:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"]}, {"name": "guzzlehttp/guzzle", "version": "6.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "f4db5a78a5ea468d4831de7f0bf9d9415e348699"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/f4db5a78a5ea468d4831de7f0bf9d9415e348699", "reference": "f4db5a78a5ea468d4831de7f0bf9d9415e348699", "shasum": ""}, "require": {"guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.0 || ^5.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "time": "2017-06-22T18:50:49+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"]}, {"name": "psr/cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T20:24:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"]}, {"name": "firebase/php-jwt", "version": "v5.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "9984a4d3a32ae7673d6971ea00bae9d0a1abba0e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/9984a4d3a32ae7673d6971ea00bae9d0a1abba0e", "reference": "9984a4d3a32ae7673d6971ea00bae9d0a1abba0e", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": " 4.8.35"}, "time": "2017-06-27T22:17:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt"}, {"name": "google/auth", "version": "v1.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/google/google-auth-library-php.git", "reference": "f3fc99fd621f339ee3d4de01bd6a709ed1396116"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/google/google-auth-library-php/zipball/f3fc99fd621f339ee3d4de01bd6a709ed1396116", "reference": "f3fc99fd621f339ee3d4de01bd6a709ed1396116", "shasum": ""}, "require": {"firebase/php-jwt": "~2.0|~3.0|~4.0|~5.0", "guzzlehttp/guzzle": "~5.3.1|~6.0", "guzzlehttp/psr7": "~1.2", "php": ">=5.4", "psr/cache": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^1.11", "guzzlehttp/promises": "0.1.1|^1.3", "phpunit/phpunit": "^4.8.36", "sebastian/comparator": ">=1.2.3"}, "time": "2017-12-06T21:27:53+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"]}, {"name": "rize/uri-template", "version": "0.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/rize/UriTemplate.git", "reference": "9e5fdd5c47147aa5adf7f760002ee591ed37b9ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rize/UriTemplate/zipball/9e5fdd5c47147aa5adf7f760002ee591ed37b9ca", "reference": "9e5fdd5c47147aa5adf7f760002ee591ed37b9ca", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.0.0"}, "time": "2017-06-14T03:57:53+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Rize\\UriTemplate": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>ut <PERSON>", "homepage": "http://twitter.com/rezigned"}], "description": "PHP URI Template (RFC 6570) supports both expansion & extraction", "keywords": ["RFC 6570", "template", "uri"]}, {"name": "google/cloud-core", "version": "v1.15.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/GoogleCloudPlatform/google-cloud-php-core.git", "reference": "3fc680b3cd190a10e9f03fcc9318445f5ada81a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GoogleCloudPlatform/google-cloud-php-core/zipball/3fc680b3cd190a10e9f03fcc9318445f5ada81a2", "reference": "3fc680b3cd190a10e9f03fcc9318445f5ada81a2", "shasum": ""}, "require": {"google/auth": "~0.9|^1.0", "guzzlehttp/guzzle": "^5.3|^6.0", "guzzlehttp/psr7": "^1.2", "monolog/monolog": "~1", "php": ">=5.5", "psr/http-message": "1.0.*", "rize/uri-template": "~0.3"}, "suggest": {"symfony/lock": "Required for the Spanner cached based session pool. Please require the following commit: 3.3.x-dev#1ba6ac9"}, "time": "2018-01-05T19:34:54+00:00", "bin": ["bin/google-cloud-batch"], "type": "library", "extra": {"component": {"id": "cloud-core", "target": "GoogleCloudPlatform/google-cloud-php-core.git", "path": "src/Core", "entry": "ServiceBuilder.php"}}, "installation-source": "dist", "autoload": {"psr-4": {"Google\\Cloud\\Core\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Cloud PHP shared dependency, providing functionality useful to all components."}, {"name": "google/cloud-storage", "version": "v1.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/GoogleCloudPlatform/google-cloud-php-storage.git", "reference": "41808194347f0a8b3eff4182b952e61f88d2bb0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GoogleCloudPlatform/google-cloud-php-storage/zipball/41808194347f0a8b3eff4182b952e61f88d2bb0d", "reference": "41808194347f0a8b3eff4182b952e61f88d2bb0d", "shasum": ""}, "require": {"google/cloud-core": "^1.14"}, "suggest": {"google/cloud-pubsub": "May be used to register a topic to receive bucket notifications.", "phpseclib/phpseclib": "May be used in place of OpenSSL for creating signed Cloud Storage URLs. Please require version ^2."}, "time": "2018-01-12T19:19:47+00:00", "type": "library", "extra": {"component": {"id": "cloud-storage", "target": "GoogleCloudPlatform/google-cloud-php-storage.git", "path": "src/Storage", "entry": "StorageClient.php"}}, "installation-source": "dist", "autoload": {"psr-4": {"Google\\Cloud\\Storage\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Cloud Storage Client for PHP"}]