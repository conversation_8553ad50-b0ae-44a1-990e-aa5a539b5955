# ************************************************************
# Sequel Pro SQL dump
# Version 5425
#
# https://www.sequelpro.com/
# https://github.com/sequelpro/sequelpro
#
# Host: 127.0.0.1 (MySQL 5.7.23)
# Database: qtm
# Generation Time: 2019-02-22 08:54:19 +0000
# ************************************************************


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
SET NAMES utf8mb4;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


# Dump of table device_recorders
# ------------------------------------------------------------

DROP TABLE IF EXISTS `device_recorders`;

CREATE TABLE `device_recorders` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `device_id` int(10) unsigned NOT NULL,
  `recorder_id` int(10) unsigned NOT NULL,
  `created_at` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `device_recorders_device_id_foreign` (`device_id`),
  KEY `device_recorders_recorder_id_foreign` (`recorder_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `device_recorders` WRITE;
/*!40000 ALTER TABLE `device_recorders` DISABLE KEYS */;

INSERT INTO `device_recorders` (`id`, `device_id`, `recorder_id`, `created_at`)
VALUES
	(1,1,1,NULL),
	(2,1,2,NULL),
	(3,1,3,NULL),
	(4,1,4,NULL),
	(5,2,1,NULL),
	(6,2,2,NULL),
	(7,2,3,NULL),
	(8,2,4,NULL),
	(9,3,1,NULL),
	(10,3,2,NULL),
	(11,3,3,NULL),
	(12,3,4,NULL),
	(13,4,1,NULL),
	(14,4,2,NULL),
	(15,4,3,NULL),
	(16,4,4,NULL),
	(17,5,1,NULL),
	(18,5,2,NULL),
	(19,5,3,NULL),
	(20,5,4,NULL),
	(21,6,1,NULL),
	(22,6,2,NULL),
	(23,6,3,NULL),
	(24,6,4,NULL),
	(25,7,1,NULL),
	(26,7,2,NULL),
	(27,7,3,NULL),
	(28,7,4,NULL),
	(29,8,1,NULL),
	(30,8,2,NULL),
	(31,8,3,NULL),
	(32,8,4,NULL),
	(33,9,1,NULL),
	(34,9,2,NULL),
	(35,9,3,NULL),
	(36,9,4,NULL),
	(37,10,1,NULL),
	(38,10,2,NULL),
	(39,10,3,NULL),
	(40,10,4,NULL),
	(41,11,1,NULL),
	(42,11,2,NULL),
	(43,11,3,NULL),
	(44,11,4,NULL),
	(45,12,1,NULL),
	(46,12,2,NULL),
	(47,12,3,NULL),
	(48,12,4,NULL),
	(49,13,1,NULL),
	(50,13,2,NULL),
	(51,13,3,NULL),
	(52,13,4,NULL),
	(53,14,1,NULL),
	(54,14,2,NULL),
	(55,14,3,NULL),
	(56,14,4,NULL),
	(57,15,1,NULL),
	(58,15,2,NULL),
	(59,15,3,NULL),
	(60,15,4,NULL),
	(61,16,1,NULL),
	(62,16,2,NULL),
	(63,16,3,NULL),
	(64,16,4,NULL),
	(65,17,1,NULL),
	(66,17,2,NULL),
	(67,17,3,NULL),
	(68,17,4,NULL),
	(69,18,1,NULL),
	(70,18,2,NULL),
	(71,18,3,NULL),
	(72,18,4,NULL),
	(73,19,1,NULL),
	(74,19,2,NULL),
	(75,19,3,NULL),
	(76,19,4,NULL),
	(77,20,1,NULL),
	(78,20,2,NULL),
	(79,20,3,NULL),
	(80,20,4,NULL),
	(81,21,1,NULL),
	(82,21,2,NULL),
	(83,21,3,NULL),
	(84,21,4,NULL);

/*!40000 ALTER TABLE `device_recorders` ENABLE KEYS */;
UNLOCK TABLES;


# Dump of table devices
# ------------------------------------------------------------

DROP TABLE IF EXISTS `devices`;

CREATE TABLE `devices` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `os_type` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `product` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `os_version` varchar(8) COLLATE utf8mb4_unicode_ci NOT NULL,
  `app_version` varchar(8) COLLATE utf8mb4_unicode_ci NOT NULL,
  `deleted_at` int(10) unsigned DEFAULT NULL,
  `updated_at` int(10) unsigned DEFAULT NULL,
  `created_at` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `devices` WRITE;
/*!40000 ALTER TABLE `devices` DISABLE KEYS */;

INSERT INTO `devices` (`id`, `os_type`, `product`, `model`, `os_version`, `app_version`, `deleted_at`, `updated_at`, `created_at`)
VALUES
	(1,'ios2','iPad 6th generation','iPad7,5','11.4.1','3.0',NULL,NULL,1550224321),
	(2,'ios3','iPad 6th generation','iPad7,6','11.4.1','3.0',NULL,NULL,1550224321),
	(3,'android','Samsung Tab S4','SM-T830','8.1.0','2.0',NULL,NULL,1550224321),
	(4,'android','Samsung Tab S4','SM-T835','8.1.0','2.0',NULL,NULL,1550224321),
	(5,'android','Samsung Tab S3','SM-T820','8.0.0','2.0',NULL,NULL,1550224321),
	(6,'android','Samsung Tab S3','SM-T825','8.0.0','1.1.0',NULL,NULL,1550224321),
	(7,'android','Samsung Tab S2','SM-T713','7.0.0','1.1.0',NULL,NULL,1550224321),
	(8,'android','Samsung Tab S2','SM-T813','7.0.0','1.1.0',NULL,NULL,1550224321),
	(9,'android','Asus Zenpad 3s 10','P027','7.0.0','1.1.0',NULL,NULL,1550224321),
	(10,'android','Asus Zenpad 3 8.0','P008','7.0.0','1.1.0',NULL,NULL,1550224321),
	(11,'android','Samsung Galaxy S9','SM-G960','8.0.0','1.1.0',NULL,NULL,1550224321),
	(12,'android','Samsung Galaxy S9+','SM-G965','8.0.0','1.1.0',NULL,NULL,1550224321),
	(13,'android','Samsung Galaxy S8','SM-G950','8.0.0','1.1.0',NULL,NULL,1550224321),
	(14,'android','Samsung Galaxy S8+','SM-G955','8.0.0','1.1.0',NULL,NULL,1550224321),
	(15,'android','LG G7','LM-G710','8.0.0','1.1.0',NULL,NULL,1550224321),
	(16,'android','LG G6','LG-H871','8.0.0','1.1.0',NULL,NULL,1550224321),
	(17,'android','LG G5','LG-H860','7.0.0','1.1.0',NULL,NULL,1550224321),
	(18,'android','Google Pixel 2','Pixel 2','9','1.1.0',NULL,NULL,1550224321),
	(19,'android','Google Pixel 2 XL','Pixel 2 XL','9','1.1.0',NULL,NULL,1550224321),
	(20,'android','Google Pixel','Pixel','9','1.1.0',NULL,NULL,1550224321),
	(21,'android','Google Pixel XL','Pixel XL','9','1.1.0',NULL,NULL,1550224321);

/*!40000 ALTER TABLE `devices` ENABLE KEYS */;
UNLOCK TABLES;


# Dump of table recorders
# ------------------------------------------------------------

DROP TABLE IF EXISTS `recorders`;

CREATE TABLE `recorders` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `hw_version` varchar(14) COLLATE utf8mb4_unicode_ci NOT NULL,
  `fw_version` varchar(8) COLLATE utf8mb4_unicode_ci NOT NULL,
  `deleted_at` int(10) unsigned DEFAULT NULL,
  `updated_at` int(10) unsigned DEFAULT NULL,
  `created_at` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `recorders` WRITE;
/*!40000 ALTER TABLE `recorders` DISABLE KEYS */;

INSERT INTO `recorders` (`id`, `hw_version`, `fw_version`, `deleted_at`, `updated_at`, `created_at`)
VALUES
	(1,'02011825100002','899cd976',NULL,NULL,1550224321),
	(2,'02011825100002','2ab4e49z',NULL,NULL,1550224321),
	(3,'02011847100002','899cd976',NULL,NULL,1550224321),
	(4,'02011847100002','2909a86a',NULL,NULL,1550224321);

/*!40000 ALTER TABLE `recorders` ENABLE KEYS */;
UNLOCK TABLES;



/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
