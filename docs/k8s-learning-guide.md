# Kubernetes 學習指南 - 基於故障排除經驗

## 📚 **核心概念解析**

### 1. 標籤選擇器 (Label Selector) - 最重要！

#### 什麼是標籤？
標籤就像貼紙，貼在 Kubernetes 資源上用來識別它們。

```yaml
# Pod 的標籤
metadata:
  labels:
    app: qtm-api        # 應用程式名稱
    env: fra            # 環境
    version: v20250620  # 版本
```

#### 服務如何找到 Pod？
服務用「選擇器」來找到對應的 Pod：

```yaml
# 服務的選擇器
spec:
  selector:
    app: qtm-api    # 找所有有這個標籤的 Pod
    env: fra        # 而且環境是 fra
```

#### 這次的問題：
```bash
# 服務要求
selector:
  app: qtm-api      # ❌ Pod 沒有這個標籤！
  env: fra
  version: v20250620

# Pod 實際有的標籤
labels:
  env: fra          # ✅ 有
  version: v20250620 # ✅ 有
  # app: qtm-api    # ❌ 沒有！
```

**結果：服務找不到 Pod → 沒有端點 → Ingress 後端不健康 → 網站無法訪問**

### 2. Kubernetes 資源檢查命令

#### 2.1 基本查看命令
```bash
# 查看 Pod 狀態
kubectl get pods                     # 列出所有 Pod
kubectl get pods -o wide             # 顯示更多資訊（IP、節點等）
kubectl get pods --show-labels       # 顯示標籤
kubectl get pods -l env=fra          # 只顯示 env=fra 的 Pod

# 查看服務
kubectl get svc                      # 列出所有服務
kubectl get svc app -o yaml          # 顯示 app 服務的完整配置

# 查看端點（最重要的檢查）
kubectl get endpoints                # 看哪些服務有端點
kubectl get endpoints app            # 看 app 服務的端點
```

#### 2.2 故障診斷命令
```bash
# 詳細描述資源（包含事件）
kubectl describe pod <pod-name>      # 看 Pod 詳細狀態
kubectl describe svc app             # 看服務詳細配置
kubectl describe ingress <ingress-name>  # 看 Ingress 狀態

# 查看日誌
kubectl logs <pod-name>              # 看 Pod 日誌
kubectl logs <pod-name> -c nginx     # 看特定容器的日誌
kubectl logs -l env=fra --tail=50    # 看所有 fra 環境 Pod 的最後50行
```

### 3. 服務發現機制

#### 3.1 Service 類型
```yaml
# NodePort - 透過節點IP訪問
type: NodePort

# ClusterIP - 只能內部訪問（預設）
type: ClusterIP

# LoadBalancer - 雲端負載均衡器
type: LoadBalancer
```

#### 3.2 這次的架構
```
使用者 → Google Load Balancer → Ingress → Service (NodePort) → Pod
                                    ↓
                              (透過標籤選擇器找到 Pod)
```

### 4. 常見的故障模式

#### 4.1 服務沒有端點
```bash
# 檢查
kubectl get endpoints app
# NAME   ENDPOINTS   AGE
# app    <none>      2y180d

# 原因：服務選擇器找不到符合條件的 Pod
# 解決：修復標籤或選擇器
```

#### 4.2 Pod 不健康
```bash
# 檢查
kubectl get pods
# NAME    READY   STATUS    RESTARTS   AGE
# app-xxx 2/3     Running   0          10m

# 原因：容器有問題或健康檢查失敗
# 解決：查看日誌、檢查配置
```

#### 4.3 Ingress 後端不健康
```bash
# 檢查
kubectl describe ingress <name>
# Backends:
#   app:80 (<none>)  ← 沒有後端IP

# 原因：服務沒有端點
# 解決：修復服務選擇器
```

## 🛠️ **實用診斷流程**

### 步驟 1：檢查 Pod
```bash
# 1. 看 Pod 是否運行
kubectl get pods -l env=fra

# 2. 看 Pod 標籤
kubectl get pods --show-labels

# 3. 如果有問題，看詳細狀態
kubectl describe pod <pod-name>
```

### 步驟 2：檢查服務端點
```bash
# 1. 看服務是否有端點
kubectl get endpoints app

# 2. 如果沒有端點，檢查選擇器
kubectl get svc app -o yaml | grep -A5 selector

# 3. 比對 Pod 標籤和服務選擇器
kubectl get pods --show-labels
```

### 步驟 3：修復選擇器（如果需要）
```bash
# 方法1：修補服務
kubectl patch svc app -p '{"spec":{"selector":{"env":"fra","version":"v20250620"}}}'

# 方法2：編輯服務
kubectl edit svc app
```

### 步驟 4：檢查 Ingress
```bash
# 看 Ingress 狀態
kubectl describe ingress <ingress-name>

# 看是否有 IP
kubectl get ingress
```

### 步驟 5：測試連線
```bash
# 內部測試（從 Pod 內）
kubectl exec <pod-name> -c nginx -- curl http://localhost/api/version

# 外部測試
curl -I https://fr-dashboard.qtmedical.com/
```

## 🔧 **緊急修復腳本解析**

### 核心邏輯解釋

#### 1. 獲取當前版本
```bash
# 找到正在運行的 Pod 的版本標籤
RUNNING_PODS=$(kubectl get pods -l env=${ENV} --field-selector=status.phase=Running -o jsonpath='{.items[*].metadata.labels.version}')
```

#### 2. 檢查標籤
```bash
# 取得 Pod 的所有標籤
POD_LABELS=$(kubectl get pod $POD_NAME --show-labels --no-headers | awk '{print $NF}')

# 檢查是否有 app=qtm-api 標籤
if echo "$POD_LABELS" | grep -q "app=qtm-api"; then
    # 有的話用完整選擇器
    SELECTOR="{\"app\": \"qtm-api\", \"env\": \"${ENV}\", \"version\": \"${CURRENT_VERSION}\"}"
else
    # 沒有的話降級使用
    SELECTOR="{\"env\": \"${ENV}\", \"version\": \"${CURRENT_VERSION}\"}"
fi
```

#### 3. 修復服務
```bash
# 用 kubectl patch 修改服務選擇器
kubectl patch svc app -p "{\"spec\":{\"selector\": $SELECTOR}}"
```

### 健康檢查邏輯

#### 1. 測試資料庫連線
```bash
# 測試 Cloud SQL Proxy (port 33061)
kubectl exec $POD_NAME -c nginx -- nc -z 127.0.0.1 33061

# 測試 nginx proxy (port 3306)  
kubectl exec $POD_NAME -c nginx -- nc -z 127.0.0.1 3306

# 測試 mysql 服務
kubectl exec $POD_NAME -c nginx -- nc -z mysql 3306
```

#### 2. 測試應用程式
```bash
# 測試健康檢查端點
kubectl exec $POD_NAME -c nginx -- curl -s -o /dev/null -w "%{http_code}" http://localhost/api/version
```

## 📖 **日常運維命令**

### 快速檢查清單
```bash
# 1. 檢查整體狀態
kubectl get pods,svc,endpoints

# 2. 檢查特定環境
kubectl get pods -l env=fra
kubectl get endpoints app mysql

# 3. 檢查 Ingress
kubectl get ingress
kubectl describe ingress <name>

# 4. 快速修復（如果確定版本）
kubectl patch svc app -p '{"spec":{"selector":{"env":"fra","version":"v20250620163756"}}}'

# 5. 測試應用程式
kubectl exec $(kubectl get pods -l env=fra -o jsonpath='{.items[0].metadata.name}') -c nginx -- curl -I http://localhost/api/version
```

### 版本切換
```bash
# 切換到指定版本
kubectl patch svc app -p '{"spec":{"selector":{"env":"fra","version":"v20250523120737"}}}'

# 檢查切換結果
kubectl get endpoints app
```

## 🎯 **學習建議**

### 1. 先掌握這些核心概念
- ✅ 標籤和選擇器的關係
- ✅ 服務發現機制
- ✅ Pod → Service → Ingress 的流程

### 2. 熟練這些檢查命令
- ✅ `kubectl get pods --show-labels`
- ✅ `kubectl get endpoints`
- ✅ `kubectl describe`

### 3. 理解故障模式
- ✅ 無端點 = 選擇器問題
- ✅ Pod 不健康 = 應用程式問題
- ✅ Ingress 不健康 = 服務問題

### 4. 實作練習
- ✅ 使用緊急修復腳本
- ✅ 手動執行診斷命令
- ✅ 理解每個步驟的作用

這些概念掌握後，您就能理解 90% 的 Kubernetes 故障排除場景！ 