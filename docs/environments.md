# 環境配置參考

## 🌍 **支援的環境**

| 環境代碼 | 環境名稱 | 區域 | Dashboard URL | API URL |
|---------|---------|------|---------------|----------|
| `sta` | Staging | US Central | https://sta-dashboard.qtmedical.com | https://sta-qtm-api.qtmedical.com |
| `pro` | Production | US Central | https://dashboard.qtmedical.com | https://qtm-api.qtmedical.com |
| `fra` | France | Europe West | https://fr-dashboard.qtmedical.com | https://fra-qtm-api.qtmedical.com |
| `aus` | Australia | Australia Southeast | https://au-dashboard.qtmedical.com | https://au-qtm-api.qtmedical.com |

## 📋 **環境對應配置**

### Ingress 配置
- **sta**: `sta-ingress-with-tls-https`
- **pro**: `prod-ingress-with-tls-https`
- **fra**: `fra-prod-ingress-with-tls-https`
- **aus**: `au-ingress-with-tls-https-002`

### HPA 配置
- **sta**: `hpa.sta.template.yaml` (1-3 replicas, 80% CPU)
- **pro**: `hpa.prod.template.yaml` (8-12 replicas, 80% CPU)
- **fra**: `hpa.fra.template.yaml` (2-6 replicas, 60% CPU)
- **aus**: `hpa.aus.template.yaml` (2-6 replicas, 60% CPU)

### 資料庫配置
```bash
# Staging
DB_NAME="sta-db-003"
DB_REGION="us-central1"

# Production  
DB_NAME="pro-db-003"
DB_REGION="us-central1"

# France
DB_NAME="fra-prod-db"
DB_REGION="europe-west9"

# Australia
DB_NAME="au-db-002"
DB_REGION="australia-southeast1"
```

### 叢集配置
```bash
# Staging
CLUSTER_NAME="sta-qtm-api"
REGION_ZONE="--region us-central1-c"

# Production
CLUSTER_NAME="pro-qtm-api" 
REGION_ZONE="--region us-central1"

# France
CLUSTER_NAME="fra-prod-qtm-api"
REGION_ZONE="--region europe-west9"

# Australia
CLUSTER_NAME="au-qtm-api"
REGION_ZONE="--zone australia-southeast1-a"
```

## 🛠️ **常用命令**

### 切換叢集
```bash
# Staging
./tools/k8s__switch_to sta-qtm-api

# Production
./tools/k8s__switch_to pro-qtm-api

# France
./tools/k8s__switch_to fra-qtm-api

# Australia
./tools/k8s__switch_to aus-qtm-api
```

### 部署到各環境
```bash
# Staging
./tools/deploy qtm-api sta master

# Production
./tools/deploy qtm-api pro master

# France
./tools/deploy-eu qtm-api fra master

# Australia
./tools/deploy qtm-api aus master
```

### 緊急修復
```bash
# Staging
./tools/emergency-fix-deployment.sh sta

# Production
./tools/emergency-fix-deployment.sh pro

# France
./tools/emergency-fix-deployment.sh fra

# Australia  
./tools/emergency-fix-deployment.sh aus
```

### 檢查服務狀態
```bash
# 檢查特定環境的 Pod
kubectl get pods -l env=fra  # 或 sta, pro, aus

# 檢查服務端點
kubectl get endpoints app mysql

# 檢查 Ingress
kubectl get ingress
```

## 🔧 **故障排除**

### 1. 檢查環境是否正確
```bash
# 確認當前連接的叢集
kubectl config current-context

# 檢查環境標籤
kubectl get pods --show-labels | grep env=
```

### 2. 常見問題
- **無端點問題**: 檢查 Pod 標籤和服務選擇器是否匹配
- **Ingress 問題**: 確認 Ingress 名稱和環境對應正確
- **資料庫連線**: 檢查 Cloud SQL Proxy 和 mysql 服務

### 3. 版本切換
```bash
# 查看當前版本
kubectl get svc app -o jsonpath='{.spec.selector.version}'

# 切換版本
kubectl patch svc app -p '{"spec":{"selector":{"env":"fra","version":"v20250620163756"}}}'
```

## 📝 **注意事項**

1. **環境隔離**: 每個環境都有獨立的叢集和資料庫
2. **配置管理**: 各環境的配置檔案位於 `configmaps/src/repos/qtm-api/env.{環境}`
3. **SSL 憑證**: 每個環境都有對應的 Google 管理憑證
4. **監控**: 每個環境都有獨立的 Slack 通知設定

## 🚨 **已移除的環境**

- ❌ `dev` - 開發環境已不再使用
- ❌ `tes` - 測試環境已不再使用 