# Kubernetes 每週學習計畫

## 🎯 **第一週：3個救命命令**

你只需要記住這 3 個命令，就能解決 80% 的問題：

### 1. 看狀態 (檢查)
```bash
kubectl get pods,svc,endpoints
```
**作用：** 一次看到 Pod、服務、端點的狀態

### 2. 看詳細 (診斷)
```bash
kubectl describe svc app
```
**作用：** 看服務的詳細配置，包含選擇器

### 3. 修復 (治療)
```bash
kubectl patch svc app -p '{"spec":{"selector":{"env":"fra","version":"v20250620163756"}}}'
```
**作用：** 修改服務的選擇器

### 實際操作練習
每天花 10 分鐘：
```bash
# 1. 檢查當前狀態
kubectl get pods,svc,endpoints

# 2. 看 app 服務詳細資訊
kubectl describe svc app

# 3. 看 Pod 有什麼標籤
kubectl get pods --show-labels
```

---

## 🎯 **第二週：理解關係**

### 概念圖
```
Pod (有標籤) ←→ Service (用選擇器找 Pod) ←→ Ingress (路由到 Service)
     ↓                    ↓                        ↓
   實際容器            虛擬地址                外部入口
```

### 4個診斷命令
```bash
# 1. 檢查 Pod 標籤
kubectl get pods --show-labels

# 2. 檢查服務選擇器  
kubectl get svc app -o yaml | grep -A5 selector

# 3. 檢查端點（最重要！）
kubectl get endpoints app

# 4. 檢查 Ingress 後端
kubectl describe ingress fra-prod-ingress-with-tls-https
```

### 故障模式識別
- **沒有端點** = 標籤不匹配
- **Pod 不健康** = 應用程式問題
- **Ingress 錯誤** = 服務問題

---

## 🎯 **第三週：動手修復**

### 使用緊急修復腳本
```bash
./tools/emergency-fix-deployment.sh fra
```

### 手動修復練習
```bash
# 1. 找出問題
kubectl get endpoints app
# 如果沒有端點...

# 2. 檢查標籤
kubectl get pods --show-labels
kubectl get svc app -o yaml | grep -A5 selector

# 3. 修復選擇器
kubectl patch svc app -p '{"spec":{"selector":{"env":"fra","version":"v實際版本"}}}'

# 4. 驗證修復
kubectl get endpoints app
```

---

## 🎯 **第四週：深入理解**

### 日誌查看
```bash
# 看應用程式日誌
kubectl logs -l env=fra -c php-fpm --tail=20
kubectl logs -l env=fra -c nginx --tail=20

# 看特定 Pod 日誌
kubectl logs app-v20250620163756-xxx -c nginx
```

### 健康檢查
```bash
# 進入 Pod 內部測試
kubectl exec -it app-v20250620163756-xxx -c nginx -- bash

# 在 Pod 內測試
curl http://localhost/api/version
nc -z 127.0.0.1 33061  # 測試資料庫連線
```

---

## 📝 **每日 5 分鐘檢查清單**

### 晨間檢查
```bash
# 1. 整體狀態
kubectl get pods,svc,endpoints

# 2. 確認網站正常
curl -I https://fr-dashboard.qtmedical.com/
```

### 問題發生時
```bash
# 1. 快速診斷
./tools/emergency-fix-deployment.sh fra

# 2. 如果腳本修復失敗，手動檢查
kubectl get endpoints app
kubectl describe svc app
```

---

## 🎓 **畢業標準**

完成這些任務就算掌握了：

### ✅ 能回答這些問題
1. 為什麼 `kubectl get endpoints app` 顯示 `<none>`？
2. Pod 有標籤但服務找不到，可能是什麼原因？
3. Ingress 顯示後端不健康，該檢查什麼？

### ✅ 能執行這些操作
1. 用一個命令檢查所有資源狀態
2. 找出服務的選擇器配置
3. 修復標籤不匹配的問題
4. 使用緊急修復腳本

### ✅ 理解這些流程
1. 用戶請求如何到達 Pod
2. 標籤選擇器的工作原理
3. 端點的作用和重要性

---

## 🚀 **實戰練習建議**

### 每週練習
- **第1週**：每天執行 3 個基本命令
- **第2週**：每天檢查一次標籤和選擇器的對應關係
- **第3週**：故意改錯選擇器，然後修復
- **第4週**：模擬各種故障場景

### 不要急著學複雜功能
先把這些基礎打好，其他功能自然就懂了：
- ✅ 標籤和選擇器
- ✅ 服務發現
- ✅ 端點機制
- ✅ 基本故障排除

這樣循序漸進，4週後您就能獨立處理大部分的 Kubernetes 問題了！ 