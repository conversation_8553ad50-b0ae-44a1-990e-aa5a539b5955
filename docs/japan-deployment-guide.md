# 日本東京地區環境部署指南

本文件詳細說明如何為 QTM API 新增日本東京地區的部署環境。

## 概述

日本環境 (jpn) 是一個新的生產環境，部署在 Google Cloud Platform 的 `asia-northeast1` (東京) 地區。

### 環境資訊
- **環境代碼**: `jpn`
- **地區**: `asia-northeast1` (東京)
- **可用區**: `asia-northeast1-a`
- **專案 ID**: `long-disk-213608`
- **集群名稱**: `jpn-prod-qtm-api`

## 前置需求

1. **GCP CLI 工具**
   ```bash
   # 確保已安裝並認證 gcloud CLI
   gcloud auth login
   gcloud config set project long-disk-213608
   ```

2. **kubectl 工具**
   ```bash
   # 確保已安裝 kubectl
   kubectl version --client
   ```

3. **必要權限**
   - GKE 集群管理權限
   - CloudSQL 管理權限
   - Cloud Storage 管理權限
   - Compute Engine 管理權限

## 步驟 1: 創建 GCP 基礎設施

執行集群創建腳本：

```bash
cd /path/to/k8s-deploy
./tools/new-japan-tokyo-cluster.sh
```

此腳本將創建以下資源：

### 1.1 GKE 集群
- **名稱**: `jpn-prod-qtm-api`
- **地區**: `asia-northeast1`
- **節點類型**: `e2-custom-4-8192` (4 vCPU, 8GB RAM)
- **磁碟大小**: 60GB SSD
- **自動擴展**: 1-6 節點

### 1.2 CloudSQL 資料庫
- **主實例**: `jpn-prod-db`
- **副本實例**: `jpn-prod-db-replica`
- **版本**: MySQL 8.0
- **規格**: 1 vCPU, 3.75GB RAM
- **儲存**: 10GB SSD (自動擴展)

### 1.3 Cloud Storage
- **Bucket 名稱**: `qtm-assets-jpn-prod`
- **地區**: `asia-northeast1`

### 1.4 網路資源
- **靜態 IPv4**: `jpn-prod-static-ip`
- **靜態 IPv6**: `jpn-prod-static-ip-ipv6`

## 步驟 2: 手動配置

### 2.1 Service Account 權限

為 utility-service account 授予 Storage Bucket 權限：

```bash
# 替換 [SERVICE_ACCOUNT_EMAIL] 為實際的 service account email
gsutil iam ch serviceAccount:[SERVICE_ACCOUNT_EMAIL]:objectViewer gs://qtm-assets-jpn-prod
```

### 2.2 DNS 配置

配置以下 DNS 記錄指向 LoadBalancer 的 EXTERNAL-IP：

- `jp-qtm-api.qtmedical.com` → `************`
- `jp-dashboard.qtmedical.com` → `************`

**注意**: 請設定 DNS A record 指向 LoadBalancer 服務的 EXTERNAL-IP 地址 `************`，而不是靜態 IP。

### 2.3 SSL 憑證

設定 Google Managed SSL 憑證：

#### 步驟 1: 創建 ManagedCertificate 資源

```bash
# 部署 Google Managed SSL 憑證
kubectl apply -f k8s-yaml/ssl-google-managed/jpn-ssl-google-managed.yaml
```

#### 步驟 2: 驗證憑證狀態

```bash
# 檢查憑證狀態
kubectl get managedcertificate jpn-google-managed-certificate
kubectl describe managedcertificate jpn-google-managed-certificate
```

#### 步驟 3: 部署 Ingress (包含 SSL 配置)

```bash
# 部署 Ingress 配置 (在確保 DNS 設定完成後)
kubectl apply -f k8s-yaml/ingress-loadbalancer-with-ssl/jpn-ingress-80-443.yaml
```

**重要注意事項**:
- 憑證配置包含域名: `jp-dashboard.qtmedical.com` 和 `jp-qtm-api.qtmedical.com`
- 憑證需要 DNS 解析正常才能完成 provisioning
- 同一個 subdomain 同時間只能掛一個 Google-managed SSL certificate
- 憑證 provisioning 可能需要 10-60 分鐘

## 步驟 3: Kubernetes 配置

### 3.1 切換到日本集群

```bash
gcloud container clusters get-credentials jpn-prod-qtm-api --region asia-northeast1 --project long-disk-213608
```

### 3.2 部署基礎服務

```bash
# 部署 Redis
kubectl apply -f k8s-yaml/redis/

# 部署 Service 配置
kubectl apply -f k8s-yaml/bundle/bundle.svc.yaml

# 部署 SSL 憑證 (如果尚未完成)
kubectl apply -f k8s-yaml/ssl-google-managed/jpn-ssl-google-managed.yaml

# 部署 Ingress 和 Load Balancer (確保 DNS 已設定)
kubectl apply -f k8s-yaml/ingress-loadbalancer-with-ssl/jpn-ingress-80-443.yaml
```

## 步驟 4: 應用程式部署

### 4.1 首次部署

```bash
# 使用 deploy-eu 腳本部署到日本環境
./tools/deploy-eu qtm-api jpn main
```

### 4.2 部署參數說明

- `qtm-api`: 應用程式名稱
- `jpn`: 環境代碼
- `main`: Git 分支名稱

## 步驟 5: 驗證部署

### 5.1 檢查 Pod 狀態

```bash
kubectl get pods -l app=qtm-api
kubectl get svc
kubectl get ingress
```

### 5.2 檢查應用程式版本

```bash
curl https://jp-qtm-api.qtmedical.com/api/version
```

### 5.3 檢查資料庫連接

```bash
# 進入 PHP-FPM 容器
./tools/k8s__exec_php_fpm_jpn

# 在容器內測試資料庫連接
php artisan tinker
>>> DB::connection()->getPdo();
```

## 工具腳本

### CloudSQL Proxy (本地開發)

```bash
# 啟動 CloudSQL Proxy 連接日本環境
./tools/open__cloudsqlproxy_jpn
```

### 進入容器調試

```bash
# 進入日本環境的 PHP-FPM 容器
./tools/k8s__exec_php_fpm_jpn
```

## 環境配置檔案

### 主要配置檔案位置

- **環境變數**: `configmaps/src/repos/qtm-api/env.jpn`
- **HPA 配置**: `k8s-yaml/bundle/hpa.jpn.template.yaml`
- **Nginx 配置**: 使用預設的 `nginx.server.conf`

### 重要配置項目

```bash
# 應用程式設定
APP_ENV=jp
APP_LOC=JP
APP_URL=https://jp-dashboard.qtmedical.com

# API 端點
API_URL=https://jp-qtm-api.qtmedical.com
DASHBOARD_URL=https://jp-dashboard.qtmedical.com

# 儲存設定
GOOGLE_CLOUD_STORAGE_BUCKET=qtm-assets-jpn-prod

# 第三方服務 (需要更新為日本地區的端點)
XML_API_URL=https://xml-service-699443111220.asia-northeast1.run.app/
IMAGE_GENERATOR_API_URL=https://jpn---ecgview-service-polqlrzria-uc.a.run.app/
HEART_AGE_API_URL=https://jpn---ai-heartage-service-ldqhpcgf3q-de.a.run.app/
```

## 監控和維護

### 日誌查看

```bash
# 查看應用程式日誌
kubectl logs -f deployment/app-[VERSION] -c php-fpm

# 查看 Nginx 日誌
kubectl logs -f deployment/app-[VERSION] -c nginx
```

### 擴展配置

HPA (Horizontal Pod Autoscaler) 配置：
- **最小副本數**: 2
- **最大副本數**: 6
- **CPU 使用率閾值**: 60%

### 備份策略

- **資料庫**: 自動每日備份，保留 7 天
- **應用程式代碼**: 存儲在 GCS bucket 中

## 故障排除

### 常見問題

1. **Pod 無法啟動**
   - 檢查 ConfigMap 是否正確部署
   - 檢查 Secret 是否存在
   - 查看 Pod 日誌

2. **資料庫連接失敗**
   - 檢查 CloudSQL Proxy 是否運行
   - 驗證資料庫實例狀態
   - 檢查網路連接

3. **靜態資源無法載入**
   - 檢查 GCS bucket 權限
   - 驗證 CDN 配置

4. **SSL 憑證問題**
   - 檢查 DNS 解析是否正確：`nslookup jp-qtm-api.qtmedical.com`
   - 檢查憑證狀態：`kubectl describe managedcertificate jpn-google-managed-certificate`
   - 等待憑證 provisioning (可能需要 10-60 分鐘)
   - 確保域名在憑證配置中正確列出

### 緊急回滾

```bash
# 如果需要回滾到前一個版本
kubectl get deployments
kubectl rollout undo deployment/app-[CURRENT_VERSION]
```

## 安全注意事項

1. **網路安全**
   - 使用 VPC 網路隔離
   - 配置適當的防火牆規則

2. **資料安全**
   - 資料庫加密傳輸
   - 定期更新密碼

3. **存取控制**
   - 使用 IAM 角色管理權限
   - 定期審查存取權限

## 聯絡資訊

如有問題，請聯絡：
- DevOps 團隊
- 系統管理員

## 部署進度報告

### ✅ 已完成的步驟

1. **基礎設施創建** - 全部完成
   - ✅ GKE 集群: `jpn-prod-qtm-api` (asia-northeast1)
   - ✅ CloudSQL: `jpn-prod-db` 和 `jpn-prod-db-replica`
   - ✅ Storage Bucket: `qtm-assets-jpn-prod`
   - ✅ 靜態 IP: IPv4 `*************`, IPv6 `2600:1901:0:4664::`
   - ✅ LoadBalancer EXTERNAL-IP: `************` (用於 DNS 設定)

2. **配置文件創建** - 全部完成
   - ✅ 環境配置: `env.jpn`
   - ✅ HPA 配置: `hpa.jpn.template.yaml`
   - ✅ 部署腳本更新: `deploy-eu` 支援 jpn 環境
   - ✅ 工具腳本: CloudSQL proxy 和 exec 腳本

3. **Kubernetes 基礎配置** - 大部分完成
   - ✅ 集群連接和認證
   - ✅ Service 配置部署 (LoadBalancer 獲得 EXTERNAL-IP: ************)
   - ✅ SSL 憑證配置部署 (狀態: Provisioning)
   - ✅ Ingress 配置部署 (使用 ingressClassName 而非已棄用的 annotation)
   - ✅ Redis 部署 (已修復 API 版本問題，Master 和 Sentinel 都正常運行)

### ✅ 已完成的部署

1. **應用程式部署** - 已完成
   - ✅ 使用 develop 分支成功部署 QTM API 應用程式
   - ✅ 應用程式版本: commit `ff225a32` (2025-06-20 14:18:23 +0800)
   - ✅ 2 個 app Pod 正常運行
   - ✅ 1 個 cron Pod 正常運行
   - ✅ API 端點正常響應: `/api/version`
   - ✅ HTTP 通過域名和 IP 都可正常訪問

2. **基礎設施配置** - 已完成
   - ✅ LoadBalancer 正常運行，EXTERNAL-IP: `************`
   - ✅ DNS 解析正常：`jp-qtm-api.qtmedical.com` → `************`
   - ✅ Redis Master 和 Sentinel 正常運行
   - ✅ ConfigMaps 已創建並正確配置

### 🔄 後續可選配置

1. **SSL 憑證配置** - 可選
   - 當前使用 HTTP LoadBalancer，完全功能正常
   - 如需 HTTPS，可選擇以下方案：
     - Google Managed SSL + Ingress (需要額外配置調試)
     - Let's Encrypt + cert-manager (較簡單的自動化方案)
     - CloudFlare SSL (外部 SSL 終止)

### 🐛 發現的問題

1. **Redis API 版本問題 (已修復)**
   ```
   error: no matches for kind "Deployment" in version "extensions/v1beta1"
   ```
   - 已更新 `redis-sentinel.yaml` 從 `extensions/v1beta1` 到 `apps/v1`
   - 添加了必要的 `selector.matchLabels` 配置
   - Redis Master 和 Sentinel 都正常運行

2. **Ingress 配置警告 (已修復)**
   - 原配置使用已棄用的 `kubernetes.io/ingress.class` annotation
   - 已更新為使用 `spec.ingressClassName: "gce"`

3. **應用程式後端缺失**
   - Ingress 配置引用 `app` 服務，但沒有運行中的 Pod 提供端點
   - 需要部署應用程式或修復現有的應用程式部署問題

## 已完成的配置文件

### 新增文件
1. `tools/new-japan-tokyo-cluster.sh` - 日本集群創建腳本
2. `configmaps/src/repos/qtm-api/env.jpn` - 日本環境配置文件
3. `k8s-yaml/bundle/hpa.jpn.template.yaml` - 日本環境 HPA 配置
4. `k8s-yaml/ssl-google-managed/jpn-ssl-google-managed.yaml` - 日本環境 SSL 憑證配置
5. `k8s-yaml/ingress-loadbalancer-with-ssl/jpn-ingress-80-443.yaml` - 日本環境 Ingress 配置
6. `tools/open__cloudsqlproxy_jpn` - 日本環境 CloudSQL Proxy 腳本
7. `tools/k8s__exec_php_fpm_jpn` - 日本環境容器執行腳本
8. `docs/japan-deployment-guide.md` - 本部署指南

### 修改文件
1. `tools/deploy-eu` - 新增 jpn 環境支援
   - CloudSQL 配置 (第 44-48 行)
   - HPA 模板選擇 (第 393-394 行)
   - 服務版本檢查 (第 677-679 行)

### 配置摘要
- **環境代碼**: jpn
- **地區**: asia-northeast1 (東京)
- **集群**: jpn-prod-qtm-api
- **資料庫**: jpn-prod-db / jpn-prod-db-replica
- **域名**: jp-qtm-api.qtmedical.com / jp-dashboard.qtmedical.com
- **儲存**: qtm-assets-jpn-prod
- **靜態 IP**: ************* (IPv4), 2600:1901:0:4664:: (IPv6)
- **LoadBalancer EXTERNAL-IP**: ************ (用於 DNS 設定)

---

**最後更新**: 2025-07-01
**版本**: 1.0
