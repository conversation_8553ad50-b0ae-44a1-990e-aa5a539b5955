# 日本東京地區環境部署指南

本文件詳細說明如何為 QTM API 新增日本東京地區的部署環境。

## 概述

日本環境 (jpn) 是一個新的生產環境，部署在 Google Cloud Platform 的 `asia-northeast1` (東京) 地區。

### 環境資訊
- **環境代碼**: `jpn`
- **地區**: `asia-northeast1` (東京)
- **可用區**: `asia-northeast1-a`
- **專案 ID**: `long-disk-213608`
- **集群名稱**: `jpn-prod-qtm-api`

## 前置需求

1. **GCP CLI 工具**
   ```bash
   # 確保已安裝並認證 gcloud CLI
   gcloud auth login
   gcloud config set project long-disk-213608
   ```

2. **kubectl 工具**
   ```bash
   # 確保已安裝 kubectl
   kubectl version --client
   ```

3. **必要權限**
   - GKE 集群管理權限
   - CloudSQL 管理權限
   - Cloud Storage 管理權限
   - Compute Engine 管理權限

## 步驟 1: 創建 GCP 基礎設施

執行集群創建腳本：

```bash
cd /path/to/k8s-deploy
./tools/new-japan-tokyo-cluster.sh
```

此腳本將創建以下資源：

### 1.1 GKE 集群
- **名稱**: `jpn-prod-qtm-api`
- **地區**: `asia-northeast1`
- **節點類型**: `e2-custom-4-8192` (4 vCPU, 8GB RAM)
- **磁碟大小**: 60GB SSD
- **自動擴展**: 1-6 節點

### 1.2 CloudSQL 資料庫
- **主實例**: `jpn-prod-db`
- **副本實例**: `jpn-prod-db-replica`
- **版本**: MySQL 8.0
- **規格**: 1 vCPU, 3.75GB RAM
- **儲存**: 10GB SSD (自動擴展)

### 1.3 Cloud Storage
- **Bucket 名稱**: `qtm-assets-jpn-prod`
- **地區**: `asia-northeast1`

### 1.4 網路資源
- **靜態 IPv4**: `jpn-prod-static-ip`
- **靜態 IPv6**: `jpn-prod-static-ip-ipv6`

## 步驟 2: 手動配置

### 2.1 Service Account 權限

為 utility-service account 授予 Storage Bucket 權限：

```bash
# 替換 [SERVICE_ACCOUNT_EMAIL] 為實際的 service account email
gsutil iam ch serviceAccount:[SERVICE_ACCOUNT_EMAIL]:objectViewer gs://qtm-assets-jpn-prod
```

### 2.2 DNS 配置

配置以下 DNS 記錄指向新創建的靜態 IP：

- `jp-qtm-api.qtmedical.com` → IPv4 靜態 IP
- `jp-dashboard.qtmedical.com` → IPv4 靜態 IP

### 2.3 SSL 憑證

設定 SSL 憑證 (使用 Google Managed SSL 或 Let's Encrypt)

## 步驟 3: Kubernetes 配置

### 3.1 切換到日本集群

```bash
gcloud container clusters get-credentials jpn-prod-qtm-api --region asia-northeast1 --project long-disk-213608
```

### 3.2 部署基礎服務

```bash
# 部署 Redis
kubectl apply -f k8s-yaml/redis/

# 部署 Service 配置
kubectl apply -f k8s-yaml/bundle/bundle.svc.yaml

# 部署 Ingress 和 Load Balancer
kubectl apply -f k8s-yaml/ingress-loadbalancer-with-ssl/
```

## 步驟 4: 應用程式部署

### 4.1 首次部署

```bash
# 使用 deploy-eu 腳本部署到日本環境
./tools/deploy-eu qtm-api jpn main
```

### 4.2 部署參數說明

- `qtm-api`: 應用程式名稱
- `jpn`: 環境代碼
- `main`: Git 分支名稱

## 步驟 5: 驗證部署

### 5.1 檢查 Pod 狀態

```bash
kubectl get pods -l app=qtm-api
kubectl get svc
kubectl get ingress
```

### 5.2 檢查應用程式版本

```bash
curl https://jp-qtm-api.qtmedical.com/api/version
```

### 5.3 檢查資料庫連接

```bash
# 進入 PHP-FPM 容器
./tools/k8s__exec_php_fpm_jpn

# 在容器內測試資料庫連接
php artisan tinker
>>> DB::connection()->getPdo();
```

## 工具腳本

### CloudSQL Proxy (本地開發)

```bash
# 啟動 CloudSQL Proxy 連接日本環境
./tools/open__cloudsqlproxy_jpn
```

### 進入容器調試

```bash
# 進入日本環境的 PHP-FPM 容器
./tools/k8s__exec_php_fpm_jpn
```

## 環境配置檔案

### 主要配置檔案位置

- **環境變數**: `configmaps/src/repos/qtm-api/env.jpn`
- **HPA 配置**: `k8s-yaml/bundle/hpa.jpn.template.yaml`
- **Nginx 配置**: 使用預設的 `nginx.server.conf`

### 重要配置項目

```bash
# 應用程式設定
APP_ENV=jp
APP_LOC=JP
APP_URL=https://jp-dashboard.qtmedical.com

# API 端點
API_URL=https://jp-qtm-api.qtmedical.com
DASHBOARD_URL=https://jp-dashboard.qtmedical.com

# 儲存設定
GOOGLE_CLOUD_STORAGE_BUCKET=qtm-assets-jpn-prod

# 第三方服務 (需要更新為日本地區的端點)
XML_API_URL=https://xml-service-699443111220.asia-northeast1.run.app/
IMAGE_GENERATOR_API_URL=https://jpn---ecgview-service-polqlrzria-uc.a.run.app/
HEART_AGE_API_URL=https://jpn---ai-heartage-service-ldqhpcgf3q-de.a.run.app/
```

## 監控和維護

### 日誌查看

```bash
# 查看應用程式日誌
kubectl logs -f deployment/app-[VERSION] -c php-fpm

# 查看 Nginx 日誌
kubectl logs -f deployment/app-[VERSION] -c nginx
```

### 擴展配置

HPA (Horizontal Pod Autoscaler) 配置：
- **最小副本數**: 2
- **最大副本數**: 6
- **CPU 使用率閾值**: 60%

### 備份策略

- **資料庫**: 自動每日備份，保留 7 天
- **應用程式代碼**: 存儲在 GCS bucket 中

## 故障排除

### 常見問題

1. **Pod 無法啟動**
   - 檢查 ConfigMap 是否正確部署
   - 檢查 Secret 是否存在
   - 查看 Pod 日誌

2. **資料庫連接失敗**
   - 檢查 CloudSQL Proxy 是否運行
   - 驗證資料庫實例狀態
   - 檢查網路連接

3. **靜態資源無法載入**
   - 檢查 GCS bucket 權限
   - 驗證 CDN 配置

### 緊急回滾

```bash
# 如果需要回滾到前一個版本
kubectl get deployments
kubectl rollout undo deployment/app-[CURRENT_VERSION]
```

## 安全注意事項

1. **網路安全**
   - 使用 VPC 網路隔離
   - 配置適當的防火牆規則

2. **資料安全**
   - 資料庫加密傳輸
   - 定期更新密碼

3. **存取控制**
   - 使用 IAM 角色管理權限
   - 定期審查存取權限

## 聯絡資訊

如有問題，請聯絡：
- DevOps 團隊
- 系統管理員

## 已完成的配置文件

本次新增日本環境已創建/修改以下文件：

### 新增文件
1. `tools/new-japan-tokyo-cluster.sh` - 日本集群創建腳本
2. `configmaps/src/repos/qtm-api/env.jpn` - 日本環境配置文件
3. `k8s-yaml/bundle/hpa.jpn.template.yaml` - 日本環境 HPA 配置
4. `tools/open__cloudsqlproxy_jpn` - 日本環境 CloudSQL Proxy 腳本
5. `tools/k8s__exec_php_fpm_jpn` - 日本環境容器執行腳本
6. `docs/japan-deployment-guide.md` - 本部署指南

### 修改文件
1. `tools/deploy-eu` - 新增 jpn 環境支援
   - CloudSQL 配置 (第 44-48 行)
   - HPA 模板選擇 (第 393-394 行)
   - 服務版本檢查 (第 677-679 行)

### 配置摘要
- **環境代碼**: jpn
- **地區**: asia-northeast1 (東京)
- **集群**: jpn-prod-qtm-api
- **資料庫**: jpn-prod-db / jpn-prod-db-replica
- **域名**: jp-qtm-api.qtmedical.com / jp-dashboard.qtmedical.com
- **儲存**: qtm-assets-jpn-prod

---

**最後更新**: 2025-07-01
**版本**: 1.0
