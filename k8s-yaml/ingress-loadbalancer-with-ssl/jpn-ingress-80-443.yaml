# 注意！ 建立 Ingress 之前須先建立：
# - Google-managed SSL certificates
# - Global Static IP address

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jpn-prod-ingress-with-tls-https
  annotations:
    kubernetes.io/ingress.global-static-ip-name: jpn-prod-static-ip
    networking.gke.io/managed-certificates: jpn-google-managed-certificate
spec:
  ingressClassName: gce
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: app
            port:
              number: 80

# 注意：Ingress 建立之後，需手動去 Console 建立 IPv6 與此 Ingress 的關聯