apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jpn-ingress-simple
  namespace: default
  annotations:
    kubernetes.io/ingress.global-static-ip-name: "jpn-prod-static-ip"
    cloud.google.com/load-balancer-type: "External"
spec:
  ingressClassName: gce
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: app
            port:
              number: 80
