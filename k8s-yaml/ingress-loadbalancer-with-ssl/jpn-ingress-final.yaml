apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jpn-ingress-final
  namespace: default
  annotations:
    # 使用全域靜態 IP
    kubernetes.io/ingress.global-static-ip-name: "jpn-prod-static-ip"
    # 設定 Load Balancer 類型
    cloud.google.com/load-balancer-type: "External"
    # 使用 Google Managed SSL 憑證
    networking.gke.io/managed-certificates: "jpn-ssl-certificate"
    # 重定向 HTTP 到 HTTPS
    ingress.gcp.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: gce
  rules:
  - host: jp-qtm-api.qtmedical.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: app
            port:
              number: 80
  - host: jp-dashboard.qtmedical.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: app
            port:
              number: 80
