# 使用 NodePort 模式的 Ingress (不使用 NEG)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jpn-prod-ingress-with-tls-https
  annotations:
    kubernetes.io/ingress.global-static-ip-name: jpn-prod-static-ip
    networking.gke.io/managed-certificates: jpn-google-managed-certificate
    cloud.google.com/neg: '{"ingress": false}'
spec:
  ingressClassName: gce
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: app
            port:
              number: 80
