apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-sentinel
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis-sentinel
  template:
    metadata:
      labels:
        app: redis-sentinel
    spec:
      containers:
      - name: redis-sentinel
        image: gcr.io/google_containers/redis:v1
        env:
          - name: SENTINEL
            value: "true"
        ports:
          - containerPort: 26379
---
apiVersion: v1
kind: Service
metadata:
  name: redis-sentinel
spec:
  ports:
    - port: 26379
      targetPort: 26379
  selector:
    app: redis-sentinel