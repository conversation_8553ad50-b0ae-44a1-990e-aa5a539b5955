# 最原始的 L4 LoadBalancer 來 expose 80/443
apiVersion: v1
kind: Service
metadata:
  name: app
  annotations:
    cloud.google.com/backend-config: '{"default": "app-backend-config"}'
spec:
  type: NodePort
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    version: v20250702172507
  # - port: 443
  #   targetPort: 443
  #   protocol: TCP
  #   name: ssl
---
apiVersion: cloud.google.com/v1
kind: BackendConfig
metadata:
  name: app-backend-config
spec:
  healthCheck:
    checkIntervalSec: 15
    timeoutSec: 15
    healthyThreshold: 1
    unhealthyThreshold: 2
    type: HTTP
    requestPath: /api/version