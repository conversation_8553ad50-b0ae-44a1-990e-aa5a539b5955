apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-v1234567890
  labels:
    name: qtm-api
    version: v1234567890
    env: bare
spec:
  selector:
    matchLabels:
      version: v1234567890
      env: bare
  template:
    metadata:
      labels:
        version: v1234567890
        env: bare
    spec:
      containers:
        - name: nginx
          image: nginx:stable
          ports:
            - containerPort: 80
