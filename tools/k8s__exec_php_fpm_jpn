#!/usr/bin/env bash

# 日本環境 PHP-FPM 容器執行腳本
# 用於進入日本環境的 PHP-FPM 容器進行調試

set -e

# 切換到日本集群
echo "切換到日本 GKE 集群..."
gcloud container clusters get-credentials jpn-prod-qtm-api --region asia-northeast1 --project long-disk-213608

# 獲取當前運行的 pod
echo "獲取日本環境的 Pod 列表..."
kubectl get pods -l app=qtm-api

# 獲取第一個運行中的 pod
POD_NAME=$(kubectl get pods -l app=qtm-api -o jsonpath='{.items[0].metadata.name}')

if [ -z "$POD_NAME" ]; then
    echo "錯誤: 找不到運行中的 Pod"
    exit 1
fi

echo "進入 Pod: $POD_NAME"
echo "容器: php-fpm"

# 進入容器
kubectl exec -it "$POD_NAME" -c php-fpm -- /bin/bash
