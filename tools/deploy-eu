#!/usr/bin/env bash

set -e  # Exit immediately if a command exits with a non-zero status

# Function to handle errors
error_handler() {
    echo "Error occurred in script at line: $1"
    exit 1
}

# Trap errors and call the error_handler function
trap 'error_handler $LINENO' ERR

# @const DIR        bash script 的絕對路徑
# @const ROOT       bash script 的父路徑
readonly DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly ROOT="$(cd "$(dirname "${DIR}")" && pwd)"

# @const 資料夾的路徑，都用小寫好辨識
readonly repos="$ROOT/repos"
readonly bundle="$ROOT/k8s-yaml/bundle"
readonly src="../configmaps/src"
readonly dist="${ROOT}/configmaps/dist"
readonly configmaps="${ROOT}/configmaps"

# @const 輸入的變數
readonly REPO_NAME=$1
readonly REPO_ENV=$2
readonly REPO_BRANCH=$3
readonly IS_BOT=$4

# @const CloudSQL 的名稱
case "${REPO_ENV}" in
fra)
    readonly DB_NAME="fra-prod-db"
    readonly DB_REGION="europe-west9"
    readonly PROJECT_NAME="long-disk-213608"
    ;;
aus)
    readonly DB_NAME="au-db-002"
    readonly DB_REGION="australia-southeast1"
    readonly PROJECT_NAME="long-disk-213608"
    ;;
jpn)
    readonly DB_NAME="jpn-prod-db"
    readonly DB_REGION="asia-northeast1"
    readonly PROJECT_NAME="long-disk-213608"
    ;;
dev)
    readonly DB_NAME="twn-dev-db"
    readonly DB_REGION="asia-east1"
    readonly PROJECT_NAME="long-disk-213608"
    ;;
tes)
    readonly DB_NAME="tes-db-002"
    readonly DB_REGION="asia-east1"
    readonly PROJECT_NAME="long-disk-213608"
    ;;
sta)
    readonly DB_NAME="sta-db-003"
    readonly DB_REGION="us-central1"
    readonly PROJECT_NAME="long-disk-213608"
    ;;
pro)
    readonly DB_NAME="pro-db-003"
    readonly DB_REGION="us-central1"
    readonly PROJECT_NAME="long-disk-213608"
    ;;
*)
    readonly DB_NAME="twn-dev-db"
    readonly DB_REGION="asia-east1"
    readonly PROJECT_NAME="long-disk-213608"
    ;;
esac

# @const 舊版與新版的 version label
readonly VERSION_PREV="$(kubectl get svc/app -o json | jq '.spec.selector.version' -r)"
readonly VERSION_NEXT="v$(date +%Y%m%d%H%M%S)"

# # @const GCP 專案的 ID
# readonly PROJECT_NAME="long-disk-213608"

# cloud sql proxy note
readonly W_1="${PROJECT_NAME}:${DB_REGION}:${DB_NAME}=tcp:127.0.0.1:33061"
readonly R_1="${PROJECT_NAME}:${DB_REGION}:${DB_NAME}-replica=tcp:127.0.0.1:33071"

# @const Google Cloud Storage 的 bucket bane，實作 S3 deploy 上傳的地方
#        不同的環境也應該要上傳到同樣的地方，因為程式本身不應該因為環境而有所差異，
#        而是由 .env 來做設定。
readonly BUCKET_NAME="code-$REPO_NAME"

# @const 欲上傳的程式碼放的資料夾名稱
readonly REPO_DIR="${repos}/$REPO_NAME"

# @const 上傳 GCS 的 zip 叫的名字
readonly ZIP_NAME="${VERSION_NEXT}.zip"
readonly ZIP_TMP="$ROOT/$ZIP_NAME"

# @const 執行這個 script 的 OS 名稱，用於分別一些不同 OS 的指令差異。
readonly UNAME="$(uname -s)"
case "${UNAME}" in
Linux*) readonly MACHINE=Linux ;;
Darwin*) readonly MACHINE=Mac ;;
CYGWIN*) readonly MACHINE=Cygwin ;;
MINGW*) readonly MACHINE=MinGw ;;
*) readonly MACHINE="UNKNOWN:${UNAME}" ;;
esac

# @const k8s deployment 的名字
readonly DEP_PREV="app-${VERSION_PREV}"
readonly DEP_NEXT="app-${VERSION_NEXT}"
readonly CRON_PREV="cron-${VERSION_PREV}"
readonly CRON_NEXT="cron-${VERSION_NEXT}"

# 使用普通數組來存儲函數名和執行時間
function_names=()
function_times=()

# 為每個函數添加計時裝飾器
time_function() {
    local func_name=$1
    local start_time=$(date +%s.%N)
    
    # 執行原始函數
    "${@:2}"
    
    local end_time=$(date +%s.%N)
    local elapsed=$(echo "$end_time - $start_time" | bc)
    
    # 存儲函數名和執行時間
    function_names+=("$func_name")
    function_times+=("$elapsed")
}

repo__update() {
    time_function repo__update repo__update_inner
}

repo__update_inner() {
    cd $REPO_DIR

    # 一律以 remote repo 為主
    git fetch origin $REPO_BRANCH
    git reset --hard origin/$REPO_BRANCH

    # Check for git conflicts
    if git status | grep -q 'You have unmerged paths'; then
        echo "Error: Git conflicts detected. Please resolve conflicts before proceeding."
        exit 1
    fi

    git submodule update

    cd $ROOT/configmaps/src/repos/${REPO_NAME}
    cp -f env.${REPO_ENV} ${ROOT}/repos/${REPO_NAME}/.env

    cd $REPO_DIR

    docker run --rm -v "$(pwd)":/app composer:2.8.4 install --prefer-dist -o

    docker run --rm -v "$(pwd)":/app node:20 /bin/bash -c "cd /app && npm run build"
}

# 寫入 .gitinfo 讓 /api/remote-version 或是 version.php 可以抓到版本
# 將 .git 上傳會佔用空間且有風險
repo__gitinfo() {
    time_function repo__gitinfo repo__gitinfo_inner
}

repo__gitinfo_inner() {
    cd $REPO_DIR
    repo_sha=$(git rev-parse --short HEAD)
    # Simplify to just get the commit date of HEAD
    repo_desc=$(git show -s --format=%ci HEAD)

    jq --null-input \
        --arg sha "$repo_sha" \
        --arg desc "$repo_desc" \
        --arg ver "$REPO_BRANCH" \
        '{"commit": $sha, "description": $desc, "ver": $ver}' >.gitinfo
}

# customized report
repo__custom_actions() {
    time_function repo__custom_actions repo__custom_actions_inner
}

repo__custom_actions_inner() {
    TEMP_DIR="resources/views/html-report"
    case "${REPO_ENV}" in
    aus)
        # 20230502 logo customization has been implement, disable manual report template replacement
        # cp -f ${TEMP_DIR}/landscape.blade.au.php ${TEMP_DIR}/landscape.blade.php
        # cp -f ${TEMP_DIR}/summary.blade.au.php ${TEMP_DIR}/summary.blade.php
        ;;
    *) ;;

    esac
}

# 將 repo 壓縮成 zip
# 並忽略 .git
repo__compress() {
    time_function repo__compress repo__compress_inner
}

repo__compress_inner() {
    cd $repos
    zip -1rq $ZIP_TMP $REPO_NAME -x *.git*
    zip -1rq $ZIP_TMP $REPO_NAME/.gitinfo
}

# 將 repo 上傳到 GCS
repo__upload() {
    time_function repo__upload repo__upload_inner
}

repo__upload_inner() {
    # gsutil rm -r "gs://$BUCKET_NAME"/*
    # gsutil cp $ZIP_TMP "gs://$BUCKET_NAME"/
    gcloud storage cp $ZIP_TMP "gs://$BUCKET_NAME"/
    # gsutil -o GSUtil:parallel_composite_upload_threshold=150M cp $ZIP_TMP "gs://$BUCKET_NAME"/ # 似乎沒有明顯加速效果
    rm -f $ZIP_TMP
}

# 更新 configmaps
# -- configs/ 所有設定檔
# -- scripts/ 一些在環境上會用到的腳本(下載, nginx 初始化, php 初始化)
# -- secrets/ cello-cello 的 ssl, Cloud Storage 的 credential, CloudSQL 的 credential
# -- test_scripts/ 一些測試效能用的 scripts, 可以用來比較純 PHP 與 Laravel 的效能差異來做優化的上限
k8s__update_configmaps() {
    time_function k8s__update_configmaps k8s__update_configmaps_inner
}

k8s__update_configmaps_inner() {
    mkdir -p ${dist}
    cp -f ${src}/all/configs/* ${dist}/
    cp -f ${src}/all/scripts/* ${dist}/
    cp -f ${src}/all/secrets/* ${dist}/
    cp -f ${src}/all/test_scripts/* ${dist}/
    cp -f ${src}/repos/${REPO_NAME}/env.${REPO_ENV} ${dist}/.env
    if [[ (${REPO_ENV} == "aus") ]]; then
        cp -f ${src}/repos/${REPO_NAME}/nginx.server.conf.au ${dist}/nginx.server.conf
    else
        cp -f ${src}/repos/${REPO_NAME}/nginx.server.conf ${dist}/nginx.server.conf
    fi
    cp -f ${src}/repos/${REPO_NAME}/database.php ${dist}/
    kubectl delete cm configmaps

    kubectl create cm configmaps \
        --from-file=${dist} \
        -o yaml --dry-run=client | kubectl apply -f -
}

# 從 bundle.template.yaml 產生 bundle.yaml 來 deploy
# k8s__deploy_yaml() {
k8s__update_app_yaml() {
    time_function k8s__update_app_yaml k8s__update_app_yaml_inner
}

k8s__update_app_yaml_inner() {
    readonly INSTANCES="${W_1},${R_1}"

    cp -f ${bundle}/bundle.template.yaml ${bundle}/bundle.yaml

    if [[ ${MACHINE} == "Linux" ]]; then
        sed -i "s|{{VERSION}}|${VERSION_NEXT}|g" ${bundle}/bundle.yaml
        sed -i "s|{{REPO_NAME}}|${REPO_NAME}|g" ${bundle}/bundle.yaml
        sed -i "s|{{REPO_ENV}}|${REPO_ENV}|g" ${bundle}/bundle.yaml
        sed -i "s|{{REPO_BRANCH}}|${REPO_BRANCH}|g" ${bundle}/bundle.yaml
        sed -i "s|{{DEP_NAME}}|${DEP_NEXT}|g" ${bundle}/bundle.yaml
        sed -i "s|{{INSTANCES}}|${INSTANCES}|g" ${bundle}/bundle.yaml
    fi
    if [[ ${MACHINE} == "Mac" ]]; then
        sed -i '' "s|{{VERSION}}|${VERSION_NEXT}|g" ${bundle}/bundle.yaml
        sed -i '' "s|{{REPO_NAME}}|${REPO_NAME}|g" ${bundle}/bundle.yaml
        sed -i '' "s|{{REPO_ENV}}|${REPO_ENV}|g" ${bundle}/bundle.yaml
        sed -i '' "s|{{REPO_BRANCH}}|${REPO_BRANCH}|g" ${bundle}/bundle.yaml
        sed -i '' "s|{{DEP_NAME}}|${DEP_NEXT}|g" ${bundle}/bundle.yaml
        sed -i '' "s|{{INSTANCES}}|${INSTANCES}|g" ${bundle}/bundle.yaml
    fi

    kubeval ${bundle}/bundle.yaml
    status=$?
    if [ $status -eq 0 ]; then
        echo "app bundle file is valid!"
    else
        echo "app bundle file is NOT VALID!"
        exit 1
    fi
}

k8s__deploy_app_yaml() {
    time_function k8s__deploy_app_yaml k8s__deploy_app_yaml_inner
}

k8s__deploy_app_yaml_inner() {
    kubectl apply -f ${bundle}/bundle.yaml
}

k8s__update_cron_yaml() {
    time_function k8s__update_cron_yaml k8s__update_cron_yaml_inner
}

k8s__update_cron_yaml_inner() {
    readonly DB_INSTANCES="${W_1},${R_1}"
    TEMPLATE_FILE="bundle.cron.template.yaml"
    BUNDLE_FILE="bundle.cron.yaml"

    cp -f ${bundle}/${TEMPLATE_FILE} ${bundle}/${BUNDLE_FILE}

    if [[ ${MACHINE} == "Linux" ]]; then
        sed -i "s|{{VERSION}}|${VERSION_NEXT}|g" ${bundle}/${BUNDLE_FILE}
        sed -i "s|{{REPO_NAME}}|${REPO_NAME}|g" ${bundle}/${BUNDLE_FILE}
        sed -i "s|{{REPO_ENV}}|${REPO_ENV}|g" ${bundle}/${BUNDLE_FILE}
        sed -i "s|{{REPO_BRANCH}}|${REPO_BRANCH}|g" ${bundle}/${BUNDLE_FILE}
        sed -i "s|{{DEP_NAME}}|${CRON_NEXT}|g" ${bundle}/${BUNDLE_FILE}
        sed -i "s|{{INSTANCES}}|${DB_INSTANCES}|g" ${bundle}/${BUNDLE_FILE}
    fi
    if [[ ${MACHINE} == "Mac" ]]; then
        sed -i '' "s|{{VERSION}}|${VERSION_NEXT}|g" ${bundle}/${BUNDLE_FILE}
        sed -i '' "s|{{REPO_NAME}}|${REPO_NAME}|g" ${bundle}/${BUNDLE_FILE}
        sed -i '' "s|{{REPO_ENV}}|${REPO_ENV}|g" ${bundle}/${BUNDLE_FILE}
        sed -i '' "s|{{REPO_BRANCH}}|${REPO_BRANCH}|g" ${bundle}/${BUNDLE_FILE}
        sed -i '' "s|{{DEP_NAME}}|${CRON_NEXT}|g" ${bundle}/${BUNDLE_FILE}
        sed -i '' "s|{{INSTANCES}}|${DB_INSTANCES}|g" ${bundle}/${BUNDLE_FILE}
    fi

    kubeval ${bundle}/${BUNDLE_FILE}
    status=$?
    if [ $status -eq 0 ]; then
        echo "cron bundle file is valid!"
    else
        echo "cron bundle file is NOT VALID!"
        exit 1
    fi
}

k8s__deploy_cron_yaml() {
    time_function k8s__deploy_cron_yaml k8s__deploy_cron_yaml_inner
}

k8s__deploy_cron_yaml_inner() {
    kubectl apply -f ${bundle}/bundle.cron.yaml
}

# 每個 repo 客製的行為
k8s__custom_actions() {
    time_function k8s__custom_actions k8s__custom_actions_inner
}

k8s__custom_actions_inner() {
    case "${REPO_NAME}" in
    qtm-api)
        # qtm-api__queue_server
        ;;
    esac
}

k8s__scale_up_app() {
    time_function k8s__scale_up_app k8s__scale_up_app_inner
}

k8s__scale_up_app_inner() {
    if [[ (${REPO_ENV} == "pro") ]]; then
        pod_number=3
    elif [[ (${REPO_ENV} == "sta") ]]; then
        pod_number=1
    else
        pod_number=1
    fi
    kubectl scale --replicas=$pod_number deploy/app-${VERSION_NEXT}
    # kubectl scale --replicas=1 deploy/cron-${VERSION_NEXT}
}

k8s__update_hpa_yaml() {
    time_function k8s__update_hpa_yaml k8s__update_hpa_yaml_inner
}

k8s__update_hpa_yaml_inner() {
    BUNDLE_FILE="hpa.yaml"

    if [[ (${REPO_ENV} == "pro") ]]; then
        cp -f ${bundle}/hpa.prod.template.yaml ${bundle}/${BUNDLE_FILE}
    elif [[ (${REPO_ENV} == "sta") ]]; then
        cp -f ${bundle}/hpa.sta.template.yaml ${bundle}/${BUNDLE_FILE}
    elif [[ (${REPO_ENV} == "dev") ]]; then
        cp -f ${bundle}/hpa.dev.template.yaml ${bundle}/${BUNDLE_FILE}
    elif [[ (${REPO_ENV} == "aus") ]]; then
        cp -f ${bundle}/hpa.aus.template.yaml ${bundle}/${BUNDLE_FILE}
    elif [[ (${REPO_ENV} == "fra") ]]; then
        cp -f ${bundle}/hpa.fra.template.yaml ${bundle}/${BUNDLE_FILE}
    else
        cp -f ${bundle}/hpa.dev.template.yaml ${bundle}/${BUNDLE_FILE}
    fi

    if [[ ${MACHINE} == "Linux" ]]; then
        sed -i "s|{{DEP_NAME}}|${DEP_NEXT}|g" ${bundle}/${BUNDLE_FILE}
    fi
    if [[ ${MACHINE} == "Mac" ]]; then
        sed -i '' "s|{{DEP_NAME}}|${DEP_NEXT}|g" ${bundle}/${BUNDLE_FILE}
    fi

    kubeval ${bundle}/${BUNDLE_FILE}
    status=$?
    if [ $status -eq 0 ]; then
        echo "hpa bundle file is valid!"
    else
        echo "hpa bundle file is NOT VALID!"
        exit 1
    fi
}

k8s__apply_hpa() {
    time_function k8s__apply_hpa k8s__apply_hpa_inner
}

k8s__apply_hpa_inner() {
    readonly BUNDLE_FILE="hpa.yaml"

    kubectl create -f ${bundle}/${BUNDLE_FILE}
}

k8s__change_svc_target() {
    time_function k8s__change_svc_target k8s__change_svc_target_inner
}

k8s__change_svc_target_inner() {
    if ! [[ $IS_BOT == 'bot' ]]; then
        echo "Ready to deploy next version?(Y\y)"

        read y
        if ! [[ ${y} =~ ^(Y|y) ]]; then
            exit
        fi
    fi

    echo "Waiting for service to be ready..."

    # 檢查部署是否準備就緒
    while true; do
        ready=$(kubectl get deploy $DEP_NEXT -o json | jq '.status.conditions[] | select(.type == "Available") | .status' | tr -d '"')
        if [[ "$ready" == "True" ]]; then
            break
        fi
        echo -n "."
        sleep 5
    done
    echo "Deployment is ready!"

    echo "Change SVC target"
    kubectl patch svc app -p "{\"spec\":{\"selector\": {\"version\": \"${VERSION_NEXT}\"}}}"
    # 也要更新 service/app-ipv6
    kubectl patch svc app-ipv6 -p "{\"spec\":{\"selector\": {\"version\": \"${VERSION_NEXT}\"}}}"
}

k8s__quit_prev_nginx() {
    time_function k8s__quit_prev_nginx k8s__quit_prev_nginx_inner
}

k8s__quit_prev_nginx_inner() {
    echo "Quit prev nginx"

    read -r -a pods <<<"$(echo $(kubectl get pod -l "version=${VERSION_PREV}" -o name | sed 's/pod\///'))"
    for pod in "${pods[@]}"; do
        echo "POD: ${pod}"
        kubectl exec ${pod} -c nginx -- nginx -s quit
    done
}

k8s__kill_prev_deploy() {
    time_function k8s__kill_prev_deploy k8s__kill_prev_deploy_inner
}

k8s__kill_prev_deploy_inner() {
    echo "Kill prev app"

    if [[ $IS_BOT == 'bot' ]]; then
        kubectl delete deploy/${DEP_PREV}
    else
        echo "Ready to delete previous deployments?(Y\y)"
        read y
        if [[ ${y} =~ ^(Y|y) ]]; then
            kubectl delete deploy/${DEP_PREV}
        fi
    fi
}

k8s__kill_prev_cron_deploy() {
    time_function k8s__kill_prev_cron_deploy k8s__kill_prev_cron_deploy_inner
}

k8s__kill_prev_cron_deploy_inner() {
    echo "Kill prev cron"

    if [[ $IS_BOT == 'bot' ]]; then
        kubectl delete deploy/${CRON_PREV}
    else
        echo "Ready to delete previous deployments?(Y\y)"
        read y
        if [[ ${y} =~ ^(Y|y) ]]; then
            kubectl delete deploy/${CRON_PREV}
        fi
    fi
}

k8s__kill_prev_hpa() {
    time_function k8s__kill_prev_hpa k8s__kill_prev_hpa_inner
}

k8s__kill_prev_hpa_inner() {
    echo "Kill prev hpa"

    if [[ $IS_BOT == 'bot' ]]; then
        kubectl delete hpa ${DEP_PREV}
    else
        echo "Ready to delete previous deployments?(Y\y)"
        read y
        if [[ ${y} =~ ^(Y|y) ]]; then
            kubectl delete hpa ${DEP_PREV}
        fi
    fi
}

start_cron_server() {
    time_function start_cron_server start_cron_server_inner
}

start_cron_server_inner() {
    if ! [[ $IS_BOT == 'bot' ]]; then
        echo "Ready to start a Cron server?(Y\y)"
        read y
        if ! [[ ${y} =~ ^(Y|y) ]]; then
            return 1
        fi
    fi

    wait_until_pod_status_is_running

    read -r -a pods <<<$(kubectl get pods -l version=${VERSION_NEXT} -o json | jq -r ".items[].metadata.name")
    for pod in "${pods[@]}"; do
        echo "Start cron server in ${pod}"
        kubectl exec "${pod}" -- bash -c "cron"
        # only start cron server in the first pod
        break
    done
}

kill_queue_worker() {
    time_function kill_queue_worker kill_queue_worker_inner
}

kill_queue_worker_inner() {
    if ! [[ $IS_BOT == 'bot' ]]; then
        echo "Ready to kill Queue worker?(Y\y)"
        read y
        if ! [[ ${y} =~ ^(Y|y) ]]; then
            return 1
        fi

        wait_until_pod_status_is_running

        read -r -a pods <<<$(kubectl get pods -l version=${VERSION_NEXT} -o json | jq -r ".items[].metadata.name")
        for pod in "${pods[@]}"; do
            kubectl exec "${pod}" -- bash -c "ps aux | grep '/usr/bin/python /usr/bin/supervisord -c /etc/supervisor/supervisord.conf' | awk '{print \$2}' | xargs kill -9"

            kubectl exec "${pod}" -- bash -c "ps aux | grep '/var/www/html/repo/artisan queue:work' | awk '{print \$2}' | xargs kill -9"
        done

    fi
}

running_queue_worker() {
    time_function running_queue_worker running_queue_worker_inner
}

running_queue_worker_inner() {
    if ! [[ $IS_BOT == 'bot' ]]; then
        echo "Ready to Running Queue worker?(Y\y)"
        read y
        if ! [[ ${y} =~ ^(Y|y) ]]; then
            return 1
        fi
    fi

    wait_until_pod_status_is_running

    read -r -a pods <<<$(kubectl get pods -l version=${VERSION_NEXT} -o json | jq -r ".items[].metadata.name")
    for pod in "${pods[@]}"; do
        kubectl exec "${pod}" -- bash -c "supervisord -c /etc/supervisor/supervisord.conf \
            && supervisorctl reread \
            && supervisorctl update \
            && supervisorctl start laravel-worker:*"
    done
}

get_pods_currently_status() {
    time_function get_pods_currently_status get_pods_currently_status_inner
}

get_pods_currently_status_inner() {
    read pod_status <<<$(kubectl get pods -l version=${VERSION_NEXT} -o json | jq -r '.items[] | select(.status.phase == "Running") | .status.phase')
    now_total=$(grep 'Running' <<<$pod_status | wc -w | xargs)
}

wait_until_pod_status_is_running() {
    time_function wait_until_pod_status_is_running wait_until_pod_status_is_running_inner
}

wait_until_pod_status_is_running_inner() {
    read replica_total <<<$(kubectl get deploy -l version=${VERSION_NEXT} -o json | jq -r ".items[].spec.replicas" | xargs)
    # replica_total=3

    echo "replica_total=${replica_total}"

    get_pods_currently_status

    # 等到所有的 pods 狀態都變成 Running
    while [ "${replica_total}" != "${now_total}" ]; do

        get_pods_currently_status

        echo "running_pods_total=${now_total}"

        sleep 1
    done
    echo "Pods are scaled correctly"
}

qtm-api__queue_server() {
    time_function qtm-api__queue_server qtm-api__queue_server_inner
}

qtm-api__queue_server_inner() {
    wait_until_pod_status_is_running
    
    # 等待直到所有pod都準備就緒
    wait_until_pods_ready

    read -r -a pods <<<$(kubectl get pods -l version=${VERSION_NEXT} -o json | jq -r ".items[].metadata.name")
}

wait_until_pods_ready() {
    echo "Waiting for pods to be ready..."
    while true; do
        all_ready=true
        read -r -a pods <<<$(kubectl get pods -l version=${VERSION_NEXT} -o json | jq -r ".items[].metadata.name")
        for pod in "${pods[@]}"; do
            ready=$(kubectl get pod $pod -o jsonpath='{.status.containerStatuses[0].ready}')
            if [ "$ready" != "true" ]; then
                all_ready=false
                break
            fi
        done
        if $all_ready; then
            echo "All pods are ready!"
            break
        fi
        echo "Waiting for pods to be ready..."
        sleep 1
    done
}

retrieve_service_version() {
    time_function retrieve_service_version retrieve_service_version_inner
}

retrieve_service_version_inner() {
    case "${REPO_ENV}" in
    pro)
        host="qtm-api.qtmedical.com"
        ;;
    aus)
        host="au-qtm-api.qtmedical.com"
        ;;
    fra)
        host="fra-qtm-api.qtmedical.com"
        ;;
    sta)
        host="sta-qtm-api.qtmedical.com"
        ;;
    dev)
        host="dev-qtm-api.qtmedical.com"
        ;;
    esac
    
    echo "============= Version Comparison ============="
    echo "=== Current ${REPO_ENV} deployed version ==="
    
    # Try to get current version, but don't fail if unavailable
    current_version=$(curl -s https://${host}/api/version)
    
    # Check if response is HTML (error page) or empty
    if [[ "$current_version" =~ ^[[:space:]]*\<html || -z "$current_version" ]]; then
        echo "⚠️  Warning: Unable to fetch current version (API returned error or is unavailable)"
        echo "Continuing with deployment..."
    else
        # Clean control characters from the response before parsing with jq
        cleaned_version=$(echo "$current_version" | tr -d '\000-\037')
        if echo "$cleaned_version" | jq '.' >/dev/null 2>&1; then
            echo "$cleaned_version" | jq '.'
        else
            echo "⚠️  Warning: Unable to parse version response"
            echo "Raw response: $current_version"
            echo "Continuing with deployment..."
        fi
    fi
    
    # Show next version to be deployed
    cd $REPO_DIR 2>/dev/null || {
        echo "⚠️  Warning: Cannot access repo directory $REPO_DIR"
        echo "==========================================="
        return 0
    }
    
    if git fetch origin $REPO_BRANCH >/dev/null 2>&1; then
        next_sha=$(git rev-parse --short origin/$REPO_BRANCH 2>/dev/null || echo "unknown")
        next_desc=$(git show -s --format=%ci origin/$REPO_BRANCH 2>/dev/null || echo "unknown")
        
        echo -e "\n======== Next version to deploy ========="
        jq -n \
          --arg commit "$next_sha" \
          --arg description "$next_desc" \
          --arg ver "$REPO_BRANCH" \
          '{commit: $commit, description: $description, ver: $ver}'
        echo "==========================================="
        
        # Compare versions only if we have both
        if [[ -n "$cleaned_version" ]] && echo "$cleaned_version" | jq -e . >/dev/null 2>&1; then
            if [ "$(echo "$cleaned_version" | jq -r .commit 2>/dev/null)" == "$next_sha" ]; then
                echo -e "\n⚠️  Warning: Current version and next version are the same!"
            fi
        fi
    else
        echo "⚠️  Warning: Unable to fetch git information"
        echo "==========================================="
    fi
    
    return 0  # Always return success to continue deployment
}

validate__service_app() {
    time_function validate__service_app validate__service_app_inner
}

validate__service_app_inner() {
    if [ -z $VERSION_PREV ]; then
        false
    else
        true
    fi
}

function text_wait() {
    time_function text_wait text_wait_inner
}

text_wait_inner() {
    local wait_secs=${1}

    printf "Wait ${wait_secs} seconds "
    for i in $(seq 1 $wait_secs); do
        printf "."
        sleep 1
    done
    echo ""
}

check_docker_running() {
    time_function check_docker_running check_docker_running_inner
}

check_docker_running_inner() {
    if docker info > /dev/null 2>&1; then
        echo "Running"
    else
        echo "Docker is not running!!"
        exit 1
    fi
}

main() {
    if [[ -z $1 ]] || [[ -z $2 ]] || [[ -z $3 ]]; then
        echo ""
        echo "      ./deploy {repo_name} {repo_env} {repo_branch} {bot/no-update}"
        echo ""
        return 1
    fi

    check_docker_running

    retrieve_service_version
    echo "--------------------------------------------"

    k8s__update_app_yaml
    k8s__update_cron_yaml
    k8s__update_hpa_yaml

    if [[ $4 != 'no-update' ]]; then
        repo__update
    fi

    repo__gitinfo
    repo__custom_actions

    repo__compress
    repo__upload

    k8s__update_configmaps
    k8s__deploy_app_yaml
    k8s__deploy_cron_yaml

    # k8s__scale_up_app
    k8s__apply_hpa
    k8s__custom_actions

    # start_cron_server

    k8s__change_svc_target
    k8s__quit_prev_nginx
    k8s__kill_prev_deploy
    k8s__kill_prev_cron_deploy
    k8s__kill_prev_hpa

    echo ""
    echo ""
    retrieve_service_version
}


print_profiling_results() {
    echo "Function execution times:"
    for i in "${!function_names[@]}"; do
        printf "%-20s: %s seconds\n" "${function_names[i]}" "${function_times[i]}"
    done
}

if validate__service_app; then
    echo "===== DEPLOYMENT ====="
    SECONDS=0

    main "$@"

    duration=$SECONDS
    echo "===== DEPLOYMENT ===== $(($duration / 60)) minutes and $(($duration % 60)) seconds elapsed."

    # 列印出 profiling 結果
    print_profiling_results
else
    echo "Service 'app' not configured. Hint: kubectl apply -f k8s-yaml/bundle/bundle.svc.yaml"
fi
