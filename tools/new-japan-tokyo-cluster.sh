#!/usr/bin/env bash

# 日本東京地區 QTM API 集群創建腳本
# 基於現有的 eu-cluster-create.sh 和 create-cluster-au.sh 創建
# 
# 使用方法:
# ./new-japan-tokyo-cluster.sh
#
# 注意事項:
# 1. 執行前請確保已安裝並配置 gcloud CLI
# 2. 請確保有足夠的 GCP 配額
# 3. 執行後需要手動配置一些權限設定

set -e  # Exit immediately if a command exits with a non-zero status

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置變數
PROJECT="long-disk-213608"
COUNTRY_CODE="jpn"
ENV="prod"
PREFIX="${COUNTRY_CODE}-${ENV}"

CLUSTER_NAME="${PREFIX}-qtm-api"
REGION="asia-northeast1"  # Tokyo
ZONE="asia-northeast1-a"  # Tokyo Zone A
DB_INSTANCE="${PREFIX}-db"
BUCKET_NAME="qtm-assets-${PREFIX}"

# 靜態 IP 名稱
STATIC_IP_NAME="${PREFIX}-static-ip"
STATIC_IPV6_NAME="${PREFIX}-static-ip-ipv6"

log_info "開始創建日本東京地區的 QTM API 基礎設施"
log_info "專案: ${PROJECT}"
log_info "地區: ${REGION}"
log_info "集群名稱: ${CLUSTER_NAME}"
log_info "資料庫實例: ${DB_INSTANCE}"
log_info "Storage Bucket: ${BUCKET_NAME}"

# 確認執行
read -p "確定要繼續創建嗎? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_warning "操作已取消"
    exit 1
fi

# 1. 創建 GKE 集群
log_info "步驟 1: 創建 GKE 集群 ${CLUSTER_NAME}"
gcloud container --project "${PROJECT}" clusters create "${CLUSTER_NAME}" --region "${REGION}" \
    --no-enable-basic-auth --cluster-version "1.24.7-gke.900" --release-channel "stable" \
    --machine-type "e2-custom-4-8192" --image-type "COS_CONTAINERD" \
    --disk-size "60" \
    --metadata disable-legacy-endpoints=true \
    --addons HorizontalPodAutoscaling,HttpLoadBalancing \
    --enable-ip-alias \
    --enable-autoupgrade --enable-autorepair \
    --enable-shielded-nodes \
    --default-max-pods-per-node "110" \
    --enable-autoscaling --min-nodes=1 --max-nodes=6 \
    --node-locations "${ZONE}" --num-nodes=1 \
    --autoscaling-profile=optimize-utilization

if [ $? -eq 0 ]; then
    log_success "GKE 集群創建成功"
else
    log_error "GKE 集群創建失敗"
    exit 1
fi

# 2. 啟用 CloudSQL API
log_info "步驟 2: 啟用 CloudSQL API"
gcloud services enable sqladmin.googleapis.com --project "${PROJECT}"

# 3. 創建 CloudSQL 主實例
log_info "步驟 3: 創建 CloudSQL 主實例 ${DB_INSTANCE}"
gcloud sql instances create "${DB_INSTANCE}" --project="${PROJECT}" --region="${REGION}" \
    --database-version=MYSQL_8_0 --cpu=1 --memory=3.75GB \
    --storage-type=SSD --storage-size=10 --storage-auto-increase \
    --availability-type=zonal --backup --retained-backups-count=7 \
    --assign-ip --enable-bin-log \
    --root-password=qtmP@ssw0rd

if [ $? -eq 0 ]; then
    log_success "CloudSQL 主實例創建成功"
else
    log_error "CloudSQL 主實例創建失敗"
    exit 1
fi

# 4. 創建 CloudSQL 讀取副本
log_info "步驟 4: 創建 CloudSQL 讀取副本 ${DB_INSTANCE}-replica"
gcloud sql instances create "${DB_INSTANCE}-replica" --project "${PROJECT}" \
    --master-instance-name="${DB_INSTANCE}" \
    --replica-type=READ

if [ $? -eq 0 ]; then
    log_success "CloudSQL 讀取副本創建成功"
else
    log_error "CloudSQL 讀取副本創建失敗"
    exit 1
fi

# 5. 創建 Storage Bucket
log_info "步驟 5: 創建 Storage Bucket ${BUCKET_NAME}"
gcloud storage buckets create gs://${BUCKET_NAME} --project="${PROJECT}" --location="${REGION}"

if [ $? -eq 0 ]; then
    log_success "Storage Bucket 創建成功"
else
    log_error "Storage Bucket 創建失敗"
    exit 1
fi

# 6. 創建全域靜態 IP
log_info "步驟 6: 創建全域靜態 IP ${STATIC_IP_NAME}"
gcloud compute addresses create "${STATIC_IP_NAME}" --project="${PROJECT}" --global

if [ $? -eq 0 ]; then
    log_success "全域靜態 IP 創建成功"
else
    log_error "全域靜態 IP 創建失敗"
    exit 1
fi

# 7. 創建全域靜態 IPv6
log_info "步驟 7: 創建全域靜態 IPv6 ${STATIC_IPV6_NAME}"
gcloud compute addresses create "${STATIC_IPV6_NAME}" --project="${PROJECT}" --ip-version=IPV6 --global

if [ $? -eq 0 ]; then
    log_success "全域靜態 IPv6 創建成功"
else
    log_error "全域靜態 IPv6 創建失敗"
    exit 1
fi

# 8. 顯示創建的資源資訊
log_success "所有資源創建完成！"
echo
log_info "創建的資源摘要:"
echo "  - GKE 集群: ${CLUSTER_NAME} (${REGION})"
echo "  - CloudSQL 主實例: ${DB_INSTANCE}"
echo "  - CloudSQL 副本: ${DB_INSTANCE}-replica"
echo "  - Storage Bucket: gs://${BUCKET_NAME}"
echo "  - 靜態 IP: ${STATIC_IP_NAME}"
echo "  - 靜態 IPv6: ${STATIC_IPV6_NAME}"

# 9. 獲取靜態 IP 地址
log_info "獲取靜態 IP 地址資訊:"
IPV4_ADDRESS=$(gcloud compute addresses describe "${STATIC_IP_NAME}" --global --format="value(address)")
IPV6_ADDRESS=$(gcloud compute addresses describe "${STATIC_IPV6_NAME}" --global --format="value(address)")

echo "  - IPv4 地址: ${IPV4_ADDRESS}"
echo "  - IPv6 地址: ${IPV6_ADDRESS}"

# 10. 後續手動步驟提醒
echo
log_warning "後續需要手動完成的步驟:"
echo "1. 為 utility-service account 授予 Storage Bucket 的檢視權限"
echo "2. 配置 DNS 記錄指向新的靜態 IP"
echo "3. 設定 SSL 憑證"
echo "4. 更新 deploy-eu 腳本以支援 jpn 環境"
echo "5. 創建環境配置文件 (env.jpn)"
echo "6. 測試部署流程"

log_success "日本東京集群創建腳本執行完成！"
