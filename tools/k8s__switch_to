#!/usr/bin/env bash

if [[ -z $1 ]]; then
    echo ""
    echo "      ./switch_to {cluster_name}"
    echo "                  pro-qtm-api"
    echo "                  sta-qtm-api"
    echo "                  dev-qtm-api"
    echo "                  tes-qtm-api"
    echo "                  au-qtm-api"
    echo "                  jpn-qtm-api"
    echo "                  fra-prod-qtm-api"
    echo ""
    exit
fi

case $1 in
"pro-qtm-api")
    CLUSTER_NAME=$1
    REGION_ZONE="--region us-central1"
    ;;
"sta-qtm-api")
    CLUSTER_NAME=$1
    REGION_ZONE="--region us-central1-c"
    ;;
"dev-qtm-api")
    CLUSTER_NAME="twn-dev-qtm-api"
    REGION_ZONE="--region asia-east1"
    ;;
"aus-qtm-api")
    CLUSTER_NAME="au-qtm-api"
    REGION_ZONE="--zone australia-southeast1-a"
    ;;
"jpn-qtm-api")
    CLUSTER_NAME="jpn-prod-qtm-api"
    REGION_ZONE="--region asia-northeast1"
    ;;
"fra-qtm-api")
    CLUSTER_NAME="fra-prod-qtm-api"
    REGION_ZONE="--region europe-west9"
    ;;
*)
    CLUSTER_NAME="twn-dev-qtm-api"
    REGION_ZONE="--region asia-east1"
    ;;
esac

gcloud container clusters get-credentials $CLUSTER_NAME $REGION_ZONE --project long-disk-213608
