#!/usr/bin/env bash

# 日本環境 CloudSQL Proxy 連接腳本
# 用於本地開發時連接到日本地區的 CloudSQL 實例

set -e

PROJECT_ID="long-disk-213608"
REGION="asia-northeast1"
DB_INSTANCE="jpn-prod-db"
DB_REPLICA="jpn-prod-db-replica"

# CloudSQL 連接字串
MASTER_CONNECTION="${PROJECT_ID}:${REGION}:${DB_INSTANCE}=tcp:127.0.0.1:33061"
REPLICA_CONNECTION="${PROJECT_ID}:${REGION}:${DB_REPLICA}=tcp:127.0.0.1:33071"

echo "啟動日本環境 CloudSQL Proxy..."
echo "主資料庫: ${MASTER_CONNECTION}"
echo "副本資料庫: ${REPLICA_CONNECTION}"

# 檢查 cloud_sql_proxy 是否存在
if ! command -v cloud_sql_proxy &> /dev/null; then
    echo "錯誤: cloud_sql_proxy 未安裝"
    echo "請執行: ./download_cloud_sql_proxy.sh"
    exit 1
fi

# 啟動 CloudSQL Proxy
cloud_sql_proxy -instances="${MASTER_CONNECTION},${REPLICA_CONNECTION}"
