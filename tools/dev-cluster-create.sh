#!/usr/bin/env bash
set -euxo pipefail

PROJECT="long-disk-213608"

# PROJECT="qtm-service-eu"
COUNTRY_CODE="twn"
ENV="dev"
PREFIX="${COUNTRY_CODE}-${ENV}"

CLUSTER_NAME="${PREFIX}-qtm-api"
REGION="asia-east1" # Taiwan
DB_INSTANCE="${PREFIX}-db"
BUCKET_NAME="qtm-assets-${PREFIX}"

# create cluster 20221127
gcloud container --project "${PROJECT}" clusters create "${CLUSTER_NAME}" --region "${REGION}"\
    --no-enable-basic-auth --cluster-version "1.27.8-gke.1067" --release-channel "stable" \
    --machine-type "e2-custom-4-8192" --image-type "COS_CONTAINERD" \
    --disk-size "60" \
    --metadata disable-legacy-endpoints=true \
    --addons HorizontalPodAutoscaling,HttpLoadBalancing \
    --enable-ip-alias \
    --enable-autoupgrade --enable-autorepair \
    --enable-shielded-nodes \
    --default-max-pods-per-node "110" \
    --enable-autoscaling --min-nodes=1 --max-nodes=6 \
    --node-locations "${REGION}-a" --num-nodes=1 \
    --autoscaling-profile=optimize-utilization
    # --enable-autoprovisioning --enable-autoprovisioning-blue-green-upgrade \
    # --max-cpu=32 --max-memory=64 \

# cloud sql
# enable CloudSQL API
gcloud services enable sqladmin.googleapis.com --project "${PROJECT}"

# create CloudSQL master
gcloud sql instances create "${DB_INSTANCE}" --project="${PROJECT}" --region="${REGION}" \
    --database-version=MYSQL_8_0 --cpu=1 --memory=3.75GB \
    --storage-type=SSD --storage-size=10 --storage-auto-increase \
    --availability-type=zonal --backup --retained-backups-count=7 \
    --assign-ip --enable-bin-log \
    --root-password=qtmP@ssw0rd

# create CloudSQL read replica
gcloud sql instances create "${DB_INSTANCE}-replica" --project "${PROJECT}" \
    --master-instance-name="${DB_INSTANCE}" \
    --replica-type=READ

# create bucket
gcloud storage buckets create gs://${BUCKET_NAME} --project="${PROJECT}" --location="${REGION}"

# grant bucket viewer access to utility-service account
# need to add manually

# create static IP
# gcloud compute addresses create "${PREFIX}-static-ip" --project="${PROJECT}" --region=europe-west9
gcloud compute addresses create "${PREFIX}-static-ip" --project="${PROJECT}" --global

# create static IPv6IP
# gcloud compute addresses create "${PREFIX}-static-ip-ipv6" --project="${PROJECT}" --ip-version=IPV6 --global


# # delete all resources
# delete static addresses
# gcloud compute addresses delete fra-static-ip fra-static-ip-ipv6 --project="${PROJECT}"

# # delete cluster
# gcloud container clusters delete "${CLUSTER_NAME}" --project "${PROJECT}" --region "${REGION}"

# # terminate cloud sql
# gcloud sql instances delete "${DB_INSTANCE}-replica" --project "${PROJECT}"
# gcloud sql instances delete "${DB_INSTANCE}" --project "${PROJECT}"